import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  Avatar,
  IconButton,
  LinearProgress,
  Chip,
  Alert,
  Paper,
  Grid,
  CircularProgress
} from '@mui/material';
import {
  Mic,
  MicOff,
  Phone,
  PhoneDisabled,
  VolumeUp,
  VolumeOff,
  Settings,
  CallEnd
} from '@mui/icons-material';

import { websocketService, WebSocketServiceCallbacks } from '../services/websocketService';
import { audioService, AudioServiceCallbacks } from '../services/audioService';

interface VoiceCallInterfaceProps {
  serverUrl?: string;
  userName: string;
  userMobile: string;
  onCallEnd?: () => void;
}

interface CallStats {
  duration: number;
  audioChunksReceived: number;
  audioChunksSent: number;
  connectionQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

interface ConnectionStatus {
  websocket: 'connected' | 'connecting' | 'disconnected' | 'error';
  audio: 'initialized' | 'initializing' | 'error' | 'disabled';
  call: 'idle' | 'connecting' | 'active' | 'ending';
}

const VoiceCallInterface: React.FC<VoiceCallInterfaceProps> = ({
  serverUrl = 'ws://localhost:5010',
  userName,
  userMobile,
  onCallEnd
}) => {
  // State management
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    websocket: 'disconnected',
    audio: 'disabled',
    call: 'idle'
  });
  
  const [callStats, setCallStats] = useState<CallStats>({
    duration: 0,
    audioChunksReceived: 0,
    audioChunksSent: 0,
    connectionQuality: 'excellent'
  });

  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(0);
  const [transcription, setTranscription] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [streamId, setStreamId] = useState<string | null>(null);

  // Refs for timers and intervals
  const callTimerRef = useRef<number | null>(null);
  const reconnectTimerRef = useRef<number | null>(null);

  // Initialize services and callbacks
  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Set up WebSocket callbacks
        const wsCallbacks: WebSocketServiceCallbacks = {
          onConnected: () => {
            console.log('WebSocket connected');
            setConnectionStatus(prev => ({ ...prev, websocket: 'connected' }));
            setErrorMessage(null);
          },
          
          onStartReceived: (event) => {
            console.log('Call started with stream:', event.streamSid);
            setStreamId(event.streamSid);
            setConnectionStatus(prev => ({ ...prev, call: 'active' }));
            startCallTimer();
          },
          
          onMediaReceived: (event) => {
            setCallStats(prev => ({
              ...prev,
              audioChunksReceived: prev.audioChunksReceived + 1
            }));
          },
          
          onMarkReceived: (event) => {
            console.log('Mark received:', event.mark.name);
          },
          
          onStopReceived: (event) => {
            console.log('Call ended:', event.stop.reason);
            endCall();
          },
          
          onError: (error) => {
            console.error('WebSocket error:', error);
            setErrorMessage(error.message);
            setConnectionStatus(prev => ({ ...prev, websocket: 'error' }));
          },
          
          onDisconnected: () => {
            console.log('WebSocket disconnected');
            setConnectionStatus(prev => ({ ...prev, websocket: 'disconnected' }));
          }
        };

        // Set up Audio service callbacks
        const audioCallbacks: AudioServiceCallbacks = {
          onAudioData: (audioData) => {
            if (streamId && websocketService.getConnectionStatus()) {
              websocketService.sendMediaEvent(audioData, streamId);
              setCallStats(prev => ({
                ...prev,
                audioChunksSent: prev.audioChunksSent + 1
              }));
            }
          },
          
          onStartRecording: () => {
            setIsRecording(true);
          },
          
          onStopRecording: () => {
            setIsRecording(false);
          },
          
          onVolumeLevel: (level) => {
            setVolumeLevel(level);
          },
          
          onError: (error) => {
            console.error('Audio error:', error);
            setErrorMessage(error.message);
            setConnectionStatus(prev => ({ ...prev, audio: 'error' }));
          }
        };

        // Initialize services
        websocketService.setCallbacks(wsCallbacks);
        audioService.setCallbacks(audioCallbacks);

        // Initialize audio
        setConnectionStatus(prev => ({ ...prev, audio: 'initializing' }));
        await audioService.initializeAudio();
        setConnectionStatus(prev => ({ ...prev, audio: 'initialized' }));

      } catch (error) {
        console.error('Failed to initialize services:', error);
        setErrorMessage('Failed to initialize audio. Please check microphone permissions.');
        setConnectionStatus(prev => ({ ...prev, audio: 'error' }));
      }
    };

    initializeServices();

    // Cleanup on unmount
    return () => {
      if (callTimerRef.current) {
        clearInterval(callTimerRef.current);
      }
      if (reconnectTimerRef.current) {
        clearTimeout(reconnectTimerRef.current);
      }
      
      audioService.cleanup();
      websocketService.disconnect();
    };
  }, [streamId]);

  const startCallTimer = useCallback(() => {
    if (callTimerRef.current) {
      clearInterval(callTimerRef.current);
    }

    callTimerRef.current = window.setInterval(() => {
      setCallStats(prev => ({
        ...prev,
        duration: prev.duration + 1
      }));
    }, 1000);
  }, []);

  const stopCallTimer = useCallback(() => {
    if (callTimerRef.current) {
      clearInterval(callTimerRef.current);
      callTimerRef.current = null;
    }
  }, []);

  const startCall = async () => {
    try {
      setConnectionStatus(prev => ({ ...prev, websocket: 'connecting', call: 'connecting' }));
      setErrorMessage(null);

      // Connect to WebSocket
      await websocketService.connect(serverUrl);

      // Send user info (legacy compatibility)
      const userInfo = {
        type: 'store_user',
        session: `session_${Date.now()}`,
        data: {
          name: userName,
          mobile: userMobile,
          userId: userMobile,
          sessionType: 'call'
        }
      };

      // Start AI call
      const startCallMessage = {
        type: 'start_ai_call',
        session: userInfo.session
      };

      // Send messages (using legacy format for backward compatibility)
      websocketService.sendLegacyMessage(userInfo);
      websocketService.sendLegacyMessage(startCallMessage);

      // Start recording
      audioService.startRecording();

    } catch (error) {
      console.error('Failed to start call:', error);
      setErrorMessage('Failed to start call. Please try again.');
      setConnectionStatus(prev => ({ 
        ...prev, 
        websocket: 'error', 
        call: 'idle' 
      }));
    }
  };

  const endCall = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, call: 'ending' }));
    
    // Stop recording
    audioService.stopRecording();
    
    // Send end call message
    if (websocketService.getConnectionStatus()) {
      const endCallMessage = {
        type: 'end_ai_call',
        session: `session_${Date.now()}`
      };
      websocketService.sendLegacyMessage(endCallMessage);
    }
    
    // Stop timer
    stopCallTimer();
    
    // Disconnect WebSocket
    websocketService.disconnect();
    
    // Reset state
    setConnectionStatus({
      websocket: 'disconnected',
      audio: connectionStatus.audio,
      call: 'idle'
    });
    
    setStreamId(null);
    setCallStats({
      duration: 0,
      audioChunksReceived: 0,
      audioChunksSent: 0,
      connectionQuality: 'excellent'
    });

    // Notify parent component
    if (onCallEnd) {
      onCallEnd();
    }
  }, [onCallEnd, connectionStatus.audio, stopCallTimer]);

  const toggleMute = () => {
    const newMutedState = audioService.toggleMute();
    setIsMuted(newMutedState);
  };

  const sendDTMF = (digit: string) => {
    if (streamId) {
      websocketService.sendDTMFEvent(digit);
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getConnectionQualityColor = () => {
    switch (callStats.connectionQuality) {
      case 'excellent': return 'success';
      case 'good': return 'info';
      case 'fair': return 'warning';
      case 'poor': return 'error';
      default: return 'default';
    }
  };

  const isCallActive = connectionStatus.call === 'active';
  const isConnecting = connectionStatus.call === 'connecting' || connectionStatus.websocket === 'connecting';

  return (
    <Box sx={{ maxWidth: 600, mx: 'auto', p: 2 }}>
      <Card elevation={3}>
        <CardContent>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Avatar sx={{ width: 80, height: 80, mx: 'auto', mb: 2, bgcolor: 'primary.main' }}>
              {userName.charAt(0).toUpperCase()}
            </Avatar>
            <Typography variant="h5" gutterBottom>
              {userName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {userMobile}
            </Typography>
          </Box>

          {/* Error Message */}
          {errorMessage && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setErrorMessage(null)}>
              {errorMessage}
            </Alert>
          )}

          {/* Connection Status */}
          <Paper sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Chip
                  label={`WS: ${connectionStatus.websocket}`}
                  size="small"
                  color={connectionStatus.websocket === 'connected' ? 'success' : 'default'}
                />
              </Grid>
              <Grid item xs={4}>
                <Chip
                  label={`Audio: ${connectionStatus.audio}`}
                  size="small"
                  color={connectionStatus.audio === 'initialized' ? 'success' : 'default'}
                />
              </Grid>
              <Grid item xs={4}>
                <Chip
                  label={`Call: ${connectionStatus.call}`}
                  size="small"
                  color={isCallActive ? 'success' : 'default'}
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Call Stats */}
          {isCallActive && (
            <Paper sx={{ p: 2, mb: 2 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={6}>
                  <Typography variant="h6">{formatDuration(callStats.duration)}</Typography>
                  <Typography variant="caption">Call Duration</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Chip
                    label={callStats.connectionQuality}
                    size="small"
                    color={getConnectionQualityColor() as any}
                  />
                  <Typography variant="caption" display="block">
                    Quality
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    Sent: {callStats.audioChunksSent}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    Received: {callStats.audioChunksReceived}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          )}

          {/* Volume Level */}
          {isCallActive && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="caption" gutterBottom>
                Microphone Level
              </Typography>
              <LinearProgress
                variant="determinate"
                value={volumeLevel}
                sx={{ height: 8, borderRadius: 4 }}
                color={volumeLevel > 80 ? 'warning' : 'primary'}
              />
            </Box>
          )}

          {/* Audio Visualizer */}
          {isRecording && (
            <Box sx={{ mb: 2, textAlign: 'center' }}>
              <div className="audio-visualizer">
                {[...Array(7)].map((_, i) => (
                  <div key={i} className="audio-bar" />
                ))}
              </div>
              <Typography variant="caption" color="primary">
                Recording...
              </Typography>
            </Box>
          )}

          {/* Main Call Controls */}
          <Box sx={{ textAlign: 'center', mb: 2 }}>
            {!isCallActive && !isConnecting && (
              <Button
                variant="contained"
                size="large"
                startIcon={<Phone />}
                onClick={startCall}
                disabled={connectionStatus.audio !== 'initialized'}
                sx={{ minWidth: 200, py: 1.5 }}
              >
                Start Call
              </Button>
            )}

            {isConnecting && (
              <Button
                variant="outlined"
                size="large"
                disabled
                sx={{ minWidth: 200, py: 1.5 }}
              >
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Connecting...
              </Button>
            )}

            {isCallActive && (
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                <IconButton
                  onClick={toggleMute}
                  color={isMuted ? 'error' : 'primary'}
                  sx={{ bgcolor: 'background.paper', boxShadow: 2 }}
                >
                  {isMuted ? <MicOff /> : <Mic />}
                </IconButton>

                <IconButton
                  onClick={endCall}
                  color="error"
                  sx={{ bgcolor: 'error.main', color: 'white', '&:hover': { bgcolor: 'error.dark' } }}
                >
                  <CallEnd />
                </IconButton>
              </Box>
            )}
          </Box>

          {/* DTMF Keypad */}
          {isCallActive && (
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom align="center">
                DTMF Keypad
              </Typography>
              <Grid container spacing={1}>
                {['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'].map((digit) => (
                  <Grid item xs={4} key={digit}>
                    <Button
                      variant="outlined"
                      fullWidth
                      onClick={() => sendDTMF(digit)}
                      sx={{ minHeight: 48 }}
                    >
                      {digit}
                    </Button>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          )}

          {/* Stream Info */}
          {streamId && (
            <Box sx={{ mt: 2, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Stream ID: {streamId}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default VoiceCallInterface;