/**
 * Audio service for handling microphone input and audio processing
 * Converts audio to protocol-compliant format (mulaw/8000)
 */

export interface AudioConfig {
  sampleRate: number;
  bitDepth: number;
  channels: number;
  chunkDuration: number; // milliseconds
}

export interface AudioServiceCallbacks {
  onAudioData?: (audioData: ArrayBuffer) => void;
  onError?: (error: Error) => void;
  onStartRecording?: () => void;
  onStopRecording?: () => void;
  onVolumeLevel?: (level: number) => void;
}

class AudioService {
  private mediaStream: MediaStream | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private analyserNode: AnalyserNode | null = null;
  private scriptProcessor: ScriptProcessorNode | null = null;
  private callbacks: AudioServiceCallbacks = {};
  private _isRecording = false;
  private volumeMonitorInterval: number | null = null;

  // Protocol configuration (mulaw/8000)
  private readonly PROTOCOL_SAMPLE_RATE = 8000;
  private readonly PROTOCOL_BIT_DEPTH = 8;
  private readonly PROTOCOL_CHANNELS = 1;
  private readonly CHUNK_SIZE = 160; // 20ms at 8000Hz

  public setCallbacks(callbacks: AudioServiceCallbacks) {
    this.callbacks = callbacks;
  }

  public async initializeAudio(): Promise<void> {
    try {
      // Request microphone access
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 44100, // Start with high quality, we'll downsample
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // Initialize audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 44100
      });

      // Create analyzer for volume monitoring
      this.analyserNode = this.audioContext.createAnalyser();
      this.analyserNode.fftSize = 256;

      // Create script processor for real-time audio processing
      this.scriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);
      
      // Connect audio processing chain
      const source = this.audioContext.createMediaStreamSource(this.mediaStream);
      source.connect(this.analyserNode);
      this.analyserNode.connect(this.scriptProcessor);
      this.scriptProcessor.connect(this.audioContext.destination);

      // Set up audio processing
      this.scriptProcessor.onaudioprocess = (event) => {
        if (this.isRecording) {
          this.processAudioBuffer(event.inputBuffer);
        }
      };

      console.log('Audio initialized successfully');
    } catch (error) {
      console.error('Failed to initialize audio:', error);
      if (this.callbacks.onError) {
        this.callbacks.onError(new Error('Failed to access microphone'));
      }
      throw error;
    }
  }

  private processAudioBuffer(inputBuffer: AudioBuffer) {
    try {
      // Get audio data from input buffer
      const inputData = inputBuffer.getChannelData(0);
      
      // Downsample to 8000Hz
      const downsampledData = this.downsampleTo8kHz(inputData, inputBuffer.sampleRate);
      
      // Convert to 16-bit PCM
      const pcmData = this.floatToPCM16(downsampledData);
      
      // Convert to mulaw
      const mulawData = this.pcmToMulaw(pcmData);
      
      // Send in chunks
      this.sendAudioChunks(mulawData);
      
    } catch (error) {
      console.error('Error processing audio buffer:', error);
      if (this.callbacks.onError) {
        this.callbacks.onError(new Error('Audio processing failed'));
      }
    }
  }

  private downsampleTo8kHz(inputData: Float32Array, inputSampleRate: number): Float32Array {
    if (inputSampleRate === this.PROTOCOL_SAMPLE_RATE) {
      return inputData;
    }

    const ratio = inputSampleRate / this.PROTOCOL_SAMPLE_RATE;
    const outputLength = Math.floor(inputData.length / ratio);
    const outputData = new Float32Array(outputLength);

    for (let i = 0; i < outputLength; i++) {
      const inputIndex = Math.floor(i * ratio);
      outputData[i] = inputData[inputIndex];
    }

    return outputData;
  }

  private floatToPCM16(inputData: Float32Array): Int16Array {
    const outputData = new Int16Array(inputData.length);
    
    for (let i = 0; i < inputData.length; i++) {
      // Clamp and convert to 16-bit PCM
      const sample = Math.max(-1, Math.min(1, inputData[i]));
      outputData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
    }
    
    return outputData;
  }

  private pcmToMulaw(pcmData: Int16Array): Uint8Array {
    const mulawData = new Uint8Array(pcmData.length);
    
    for (let i = 0; i < pcmData.length; i++) {
      mulawData[i] = this.linearToMulaw(pcmData[i]);
    }
    
    return mulawData;
  }

  private linearToMulaw(sample: number): number {
    // Mulaw compression algorithm
    const BIAS = 0x84;
    const CLIP = 32635;
    
    let sign = (sample >> 8) & 0x80;
    if (sign !== 0) {
      sample = -sample;
    }
    
    if (sample > CLIP) {
      sample = CLIP;
    }
    
    sample += BIAS;
    
    let exponent = 0;
    let mantissa = 0;
    
    if (sample >= 256) {
      exponent = 1;
      sample >>= 1;
      while (sample >= 256 && exponent < 7) {
        exponent++;
        sample >>= 1;
      }
    }
    
    mantissa = (sample >> 4) & 0x0F;
    
    return ~(sign | (exponent << 4) | mantissa);
  }

  private sendAudioChunks(mulawData: Uint8Array) {
    // Send data in 20ms chunks (160 samples at 8kHz)
    for (let i = 0; i < mulawData.length; i += this.CHUNK_SIZE) {
      const chunk = mulawData.slice(i, i + this.CHUNK_SIZE);
      
      if (this.callbacks.onAudioData) {
        this.callbacks.onAudioData(chunk.buffer);
      }
    }
  }

  public startRecording(): void {
    if (!this.audioContext || !this.mediaStream) {
      throw new Error('Audio not initialized');
    }

    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }

    this.isRecording = true;
    this.startVolumeMonitoring();

    if (this.callbacks.onStartRecording) {
      this.callbacks.onStartRecording();
    }

    console.log('Recording started');
  }

  public stopRecording(): void {
    this.isRecording = false;
    this.stopVolumeMonitoring();

    if (this.callbacks.onStopRecording) {
      this.callbacks.onStopRecording();
    }

    console.log('Recording stopped');
  }

  private startVolumeMonitoring() {
    if (!this.analyserNode) return;

    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);
    
    const updateVolume = () => {
      if (!this.isRecording || !this.analyserNode) return;
      
      this.analyserNode.getByteFrequencyData(dataArray);
      
      // Calculate volume level (0-100)
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      const volumeLevel = Math.round((average / 255) * 100);
      
      if (this.callbacks.onVolumeLevel) {
        this.callbacks.onVolumeLevel(volumeLevel);
      }
    };

    this.volumeMonitorInterval = window.setInterval(updateVolume, 100);
  }

  private stopVolumeMonitoring() {
    if (this.volumeMonitorInterval) {
      clearInterval(this.volumeMonitorInterval);
      this.volumeMonitorInterval = null;
    }
  }

  public toggleMute(): boolean {
    if (!this.mediaStream) return false;

    const audioTracks = this.mediaStream.getAudioTracks();
    if (audioTracks.length > 0) {
      const currentState = audioTracks[0].enabled;
      audioTracks[0].enabled = !currentState;
      return !currentState; // Return new muted state
    }
    
    return false;
  }

  public isMuted(): boolean {
    if (!this.mediaStream) return true;

    const audioTracks = this.mediaStream.getAudioTracks();
    return audioTracks.length === 0 || !audioTracks[0].enabled;
  }

  public isRecording(): boolean {
    return this._isRecording;
  }

  public getVolumeLevel(): number {
    if (!this.analyserNode) return 0;

    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);
    this.analyserNode.getByteFrequencyData(dataArray);
    
    let sum = 0;
    for (let i = 0; i < dataArray.length; i++) {
      sum += dataArray[i];
    }
    
    return Math.round((sum / dataArray.length / 255) * 100);
  }

  public cleanup(): void {
    this.stopRecording();
    this.stopVolumeMonitoring();

    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect();
      this.scriptProcessor = null;
    }

    if (this.analyserNode) {
      this.analyserNode.disconnect();
      this.analyserNode = null;
    }

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    console.log('Audio service cleaned up');
  }

  // Utility methods for testing
  public async testMicrophone(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Microphone test failed:', error);
      return false;
    }
  }

  public async getAudioDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      console.error('Failed to get audio devices:', error);
      return [];
    }
  }
}

export const audioService = new AudioService();