/**
 * WebSocket service for bi-directional audio streaming protocol
 * Implements the protocol events as specified in the integration document
 */

import { io, Socket } from 'socket.io-client';

export interface StreamMetadata {
  streamSid: string;
  accountSid: string;
  callSid: string;
  from: string;
  to: string;
  mediaFormat: {
    encoding: string;
    sampleRate: number;
    bitRate: number;
    bitDepth: number;
  };
  customParameters: Record<string, any>;
}

export interface MediaEvent {
  event: 'media';
  sequenceNumber: string;
  media: {
    chunk: string;
    timestamp: string;
    payload: string; // base64 encoded audio
  };
  streamSid: string;
}

export interface MarkEvent {
  event: 'mark';
  sequenceNumber: string;
  streamSid: string;
  mark: {
    name: string;
  };
}

export interface ConnectedEvent {
  event: 'connected';
}

export interface StartEvent {
  event: 'start';
  sequenceNumber: string;
  start: StreamMetadata;
  streamSid: string;
}

export interface StopEvent {
  event: 'stop';
  sequenceNumber: string;
  stop: {
    accountSid: string;
    callSid: string;
    reason: string;
  };
  streamSid: string;
}

export interface ClearEvent {
  event: 'clear';
  streamSid: string;
}

export interface DTMFEvent {
  event: 'dtmf';
  streamSid: string;
  sequenceNumber: string;
  dtmf: {
    digit: string;
  };
}

export type ProtocolEvent = 
  | ConnectedEvent 
  | StartEvent 
  | MediaEvent 
  | StopEvent 
  | MarkEvent 
  | ClearEvent 
  | DTMFEvent;

export interface AudioConfig {
  sampleRate: number;
  bitDepth: number;
  channels: number;
  encoding: string;
}

export interface WebSocketServiceCallbacks {
  onConnected?: () => void;
  onStartReceived?: (event: StartEvent) => void;
  onMediaReceived?: (event: MediaEvent) => void;
  onMarkReceived?: (event: MarkEvent) => void;
  onStopReceived?: (event: StopEvent) => void;
  onClearReceived?: (event: ClearEvent) => void;
  onDTMFReceived?: (event: DTMFEvent) => void;
  onError?: (error: Error) => void;
  onDisconnected?: () => void;
  onAudioPlaybackComplete?: (markName: string) => void;
}

class WebSocketService {
  private socket: WebSocket | null = null;
  private callbacks: WebSocketServiceCallbacks = {};
  private sequenceNumber = 0;
  private streamSid: string | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  // Audio processing
  private audioContext: AudioContext | null = null;
  private audioQueue: AudioBuffer[] = [];
  private isPlayingAudio = false;
  private currentMarkName: string | null = null;

  constructor() {
    this.initializeAudioContext();
  }

  private initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.error('Failed to initialize audio context:', error);
    }
  }

  public setCallbacks(callbacks: WebSocketServiceCallbacks) {
    this.callbacks = callbacks;
  }

  public connect(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = new WebSocket(url);

        this.socket.onopen = () => {
          console.log('WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Send connected event
          this.sendConnectedEvent();
          
          if (this.callbacks.onConnected) {
            this.callbacks.onConnected();
          }
          resolve();
        };

        this.socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleProtocolEvent(data);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
            if (this.callbacks.onError) {
              this.callbacks.onError(new Error('Failed to parse message'));
            }
          }
        };

        this.socket.onclose = () => {
          console.log('WebSocket disconnected');
          this.isConnected = false;
          
          if (this.callbacks.onDisconnected) {
            this.callbacks.onDisconnected();
          }
          
          // Attempt to reconnect
          this.attemptReconnect(url);
        };

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);
          if (this.callbacks.onError) {
            this.callbacks.onError(new Error('WebSocket connection error'));
          }
          reject(error);
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  private attemptReconnect(url: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(url);
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
      if (this.callbacks.onError) {
        this.callbacks.onError(new Error('Failed to reconnect after maximum attempts'));
      }
    }
  }

  private handleProtocolEvent(event: ProtocolEvent) {
    console.log('Received protocol event:', event.event);

    switch (event.event) {
      case 'connected':
        // Handle connected response
        break;
        
      case 'start':
        this.streamSid = event.streamSid;
        if (this.callbacks.onStartReceived) {
          this.callbacks.onStartReceived(event);
        }
        break;
        
      case 'media':
        this.handleMediaEvent(event);
        if (this.callbacks.onMediaReceived) {
          this.callbacks.onMediaReceived(event);
        }
        break;
        
      case 'mark':
        this.handleMarkEvent(event);
        if (this.callbacks.onMarkReceived) {
          this.callbacks.onMarkReceived(event);
        }
        break;
        
      case 'stop':
        if (this.callbacks.onStopReceived) {
          this.callbacks.onStopReceived(event);
        }
        break;
        
      case 'clear':
        this.clearAudioBuffer();
        if (this.callbacks.onClearReceived) {
          this.callbacks.onClearReceived(event);
        }
        break;
        
      case 'dtmf':
        if (this.callbacks.onDTMFReceived) {
          this.callbacks.onDTMFReceived(event);
        }
        break;
    }
  }

  private async handleMediaEvent(event: MediaEvent) {
    try {
      // Decode base64 audio payload
      const audioData = this.base64ToArrayBuffer(event.media.payload);
      
      // Convert mulaw to PCM and create AudioBuffer
      const audioBuffer = await this.convertMulawToPCM(audioData);
      
      // Queue for playback
      this.audioQueue.push(audioBuffer);
      
      // Start playback if not already playing
      if (!this.isPlayingAudio) {
        this.playNextAudio();
      }
      
    } catch (error) {
      console.error('Error handling media event:', error);
    }
  }

  private handleMarkEvent(event: MarkEvent) {
    // Handle mark completion
    if (this.callbacks.onAudioPlaybackComplete) {
      this.callbacks.onAudioPlaybackComplete(event.mark.name);
    }
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = window.atob(base64);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  }

  private async convertMulawToPCM(mulawData: ArrayBuffer): Promise<AudioBuffer> {
    if (!this.audioContext) {
      throw new Error('Audio context not initialized');
    }

    // Convert mulaw to 16-bit PCM
    const mulawArray = new Uint8Array(mulawData);
    const pcmArray = new Int16Array(mulawArray.length);
    
    // Mulaw to linear conversion table (simplified)
    for (let i = 0; i < mulawArray.length; i++) {
      pcmArray[i] = this.mulawToLinear(mulawArray[i]);
    }

    // Create AudioBuffer
    const audioBuffer = this.audioContext.createBuffer(1, pcmArray.length, 8000);
    const channelData = audioBuffer.getChannelData(0);
    
    // Convert to float32 and normalize
    for (let i = 0; i < pcmArray.length; i++) {
      channelData[i] = pcmArray[i] / 32768.0;
    }

    return audioBuffer;
  }

  private mulawToLinear(mulaw: number): number {
    // Simplified mulaw to linear conversion
    mulaw = ~mulaw;
    const sign = mulaw & 0x80;
    const exponent = (mulaw >> 4) & 0x07;
    const mantissa = mulaw & 0x0F;
    
    let sample = mantissa << (exponent + 3);
    if (exponent !== 0) {
      sample += 0x84 << exponent;
    }
    
    return sign ? -sample : sample;
  }

  private async playNextAudio() {
    if (this.audioQueue.length === 0 || !this.audioContext) {
      this.isPlayingAudio = false;
      return;
    }

    this.isPlayingAudio = true;
    const audioBuffer = this.audioQueue.shift()!;
    
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(this.audioContext.destination);
    
    source.onended = () => {
      // Send mark completion if we have a current mark
      if (this.currentMarkName) {
        this.sendMarkEvent(this.currentMarkName);
        this.currentMarkName = null;
      }
      
      // Play next audio
      this.playNextAudio();
    };
    
    source.start();
  }

  private clearAudioBuffer() {
    this.audioQueue = [];
    
    // Send completion for all pending marks
    if (this.currentMarkName) {
      this.sendMarkEvent(this.currentMarkName);
      this.currentMarkName = null;
    }
  }

  // Protocol event sending methods
  public sendConnectedEvent() {
    const event: ConnectedEvent = {
      event: 'connected'
    };
    this.send(event);
  }

  public sendMediaEvent(audioData: ArrayBuffer, streamSid: string) {
    if (!this.isConnected || !this.socket) {
      console.warn('Cannot send media event: not connected');
      return;
    }

    this.sequenceNumber++;
    
    const event: MediaEvent = {
      event: 'media',
      sequenceNumber: this.sequenceNumber.toString(),
      media: {
        chunk: '1', // Simplified chunk numbering
        timestamp: Date.now().toString(),
        payload: this.arrayBufferToBase64(audioData)
      },
      streamSid
    };
    
    this.send(event);
  }

  public sendMarkEvent(markName: string) {
    if (!this.isConnected || !this.socket || !this.streamSid) {
      console.warn('Cannot send mark event: not connected or no stream');
      return;
    }

    this.sequenceNumber++;
    
    const event: MarkEvent = {
      event: 'mark',
      sequenceNumber: this.sequenceNumber.toString(),
      streamSid: this.streamSid,
      mark: {
        name: markName
      }
    };
    
    this.send(event);
  }

  public sendDTMFEvent(digit: string) {
    if (!this.isConnected || !this.socket || !this.streamSid) {
      console.warn('Cannot send DTMF event: not connected or no stream');
      return;
    }

    this.sequenceNumber++;
    
    const event: DTMFEvent = {
      event: 'dtmf',
      streamSid: this.streamSid,
      sequenceNumber: this.sequenceNumber.toString(),
      dtmf: {
        digit
      }
    };
    
    this.send(event);
  }

  public sendClearEvent() {
    if (!this.isConnected || !this.socket || !this.streamSid) {
      console.warn('Cannot send clear event: not connected or no stream');
      return;
    }

    const event: ClearEvent = {
      event: 'clear',
      streamSid: this.streamSid
    };
    
    this.send(event);
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  private send(event: ProtocolEvent | any) {
    if (this.socket && this.isConnected) {
      this.socket.send(JSON.stringify(event));
    }
  }

  // Public method for legacy message sending (backward compatibility)
  public sendLegacyMessage(message: any) {
    this.send(message);
  }

  public disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.isConnected = false;
    this.streamSid = null;
    this.sequenceNumber = 0;
  }

  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  public getStreamId(): string | null {
    return this.streamSid;
  }
}

export const websocketService = new WebSocketService();