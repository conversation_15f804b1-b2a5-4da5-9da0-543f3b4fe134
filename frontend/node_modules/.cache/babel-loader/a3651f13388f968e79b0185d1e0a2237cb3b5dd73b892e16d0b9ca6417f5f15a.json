{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActions', slot);\n}\nconst cardActionsClasses = generateUtilityClasses('MuiCardActions', ['root', 'spacing']);\nexport default cardActionsClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getCardActionsUtilityClass", "slot", "cardActionsClasses"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/CardActions/cardActionsClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActions', slot);\n}\nconst cardActionsClasses = generateUtilityClasses('MuiCardActions', ['root', 'spacing']);\nexport default cardActionsClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOF,oBAAoB,CAAC,gBAAgB,EAAEE,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACxF,eAAeI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}