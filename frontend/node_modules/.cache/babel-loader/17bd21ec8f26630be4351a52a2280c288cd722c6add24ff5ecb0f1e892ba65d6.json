{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"BackdropProps\", \"children\", \"className\", \"elevation\", \"hideBackdrop\", \"ModalProps\", \"onClose\", \"open\", \"PaperProps\", \"SlideProps\", \"TransitionComponent\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport Modal from '../Modal';\nimport Slide from '../Slide';\nimport Paper from '../Paper';\nimport capitalize from '../utils/capitalize';\nimport useTheme from '../styles/useTheme';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { getDrawerUtilityClass } from './drawerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, (ownerState.variant === 'permanent' || ownerState.variant === 'persistent') && styles.docked, styles.modal];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    anchor,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    docked: [(variant === 'permanent' || variant === 'persistent') && 'docked'],\n    modal: ['modal'],\n    paper: ['paper', `paperAnchor${capitalize(anchor)}`, variant !== 'temporary' && `paperAnchorDocked${capitalize(anchor)}`]\n  };\n  return composeClasses(slots, getDrawerUtilityClass, classes);\n};\nconst DrawerRoot = styled(Modal, {\n  name: 'MuiDrawer',\n  slot: 'Root',\n  overridesResolver\n})(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.drawer\n}));\nconst DrawerDockedRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp,\n  name: 'MuiDrawer',\n  slot: 'Docked',\n  skipVariantsResolver: false,\n  overridesResolver\n})({\n  flex: '0 0 auto'\n});\nconst DrawerPaper = styled(Paper, {\n  name: 'MuiDrawer',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.paper, styles[`paperAnchor${capitalize(ownerState.anchor)}`], ownerState.variant !== 'temporary' && styles[`paperAnchorDocked${capitalize(ownerState.anchor)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  overflowY: 'auto',\n  display: 'flex',\n  flexDirection: 'column',\n  height: '100%',\n  flex: '1 0 auto',\n  zIndex: (theme.vars || theme).zIndex.drawer,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  // temporary style\n  position: 'fixed',\n  top: 0,\n  // We disable the focus ring for mouse, touch and keyboard users.\n  // At some point, it would be better to keep it for keyboard users.\n  // :focus-ring CSS pseudo-class will help.\n  outline: 0\n}, ownerState.anchor === 'left' && {\n  left: 0\n}, ownerState.anchor === 'top' && {\n  top: 0,\n  left: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'right' && {\n  right: 0\n}, ownerState.anchor === 'bottom' && {\n  top: 'auto',\n  left: 0,\n  bottom: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'left' && ownerState.variant !== 'temporary' && {\n  borderRight: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'top' && ownerState.variant !== 'temporary' && {\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'right' && ownerState.variant !== 'temporary' && {\n  borderLeft: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'bottom' && ownerState.variant !== 'temporary' && {\n  borderTop: `1px solid ${(theme.vars || theme).palette.divider}`\n}));\nconst oppositeDirection = {\n  left: 'right',\n  right: 'left',\n  top: 'down',\n  bottom: 'up'\n};\nexport function isHorizontal(anchor) {\n  return ['left', 'right'].indexOf(anchor) !== -1;\n}\nexport function getAnchor({\n  direction\n}, anchor) {\n  return direction === 'rtl' && isHorizontal(anchor) ? oppositeDirection[anchor] : anchor;\n}\n\n/**\n * The props of the [Modal](/material-ui/api/modal/) component are available\n * when `variant=\"temporary\"` is set.\n */\nconst Drawer = /*#__PURE__*/React.forwardRef(function Drawer(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDrawer'\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor: anchorProp = 'left',\n      BackdropProps,\n      children,\n      className,\n      elevation = 16,\n      hideBackdrop = false,\n      ModalProps: {\n        BackdropProps: BackdropPropsProp\n      } = {},\n      onClose,\n      open = false,\n      PaperProps = {},\n      SlideProps,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Slide,\n      transitionDuration = defaultTransitionDuration,\n      variant = 'temporary'\n    } = props,\n    ModalProps = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n\n  // Let's assume that the Drawer will always be rendered on user space.\n  // We use this state is order to skip the appear transition during the\n  // initial mount of the component.\n  const mounted = React.useRef(false);\n  React.useEffect(() => {\n    mounted.current = true;\n  }, []);\n  const anchorInvariant = getAnchor({\n    direction: isRtl ? 'rtl' : 'ltr'\n  }, anchorProp);\n  const anchor = anchorProp;\n  const ownerState = _extends({}, props, {\n    anchor,\n    elevation,\n    open,\n    variant\n  }, other);\n  const classes = useUtilityClasses(ownerState);\n  const drawer = /*#__PURE__*/_jsx(DrawerPaper, _extends({\n    elevation: variant === 'temporary' ? elevation : 0,\n    square: true\n  }, PaperProps, {\n    className: clsx(classes.paper, PaperProps.className),\n    ownerState: ownerState,\n    children: children\n  }));\n  if (variant === 'permanent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: drawer\n    }));\n  }\n  const slidingDrawer = /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    direction: oppositeDirection[anchorInvariant],\n    timeout: transitionDuration,\n    appear: mounted.current\n  }, SlideProps, {\n    children: drawer\n  }));\n  if (variant === 'persistent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: slidingDrawer\n    }));\n  }\n\n  // variant === temporary\n  return /*#__PURE__*/_jsx(DrawerRoot, _extends({\n    BackdropProps: _extends({}, BackdropProps, BackdropPropsProp, {\n      transitionDuration\n    }),\n    className: clsx(classes.root, classes.modal, className),\n    open: open,\n    ownerState: ownerState,\n    onClose: onClose,\n    hideBackdrop: hideBackdrop,\n    ref: ref\n  }, other, ModalProps, {\n    children: slidingDrawer\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Drawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Side from which the drawer will appear.\n   * @default 'left'\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * @ignore\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The elevation of the drawer.\n   * @default 16\n   */\n  elevation: integerPropType,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Props applied to the [`Modal`](/material-ui/api/modal/) element.\n   * @default {}\n   */\n  ModalProps: PropTypes.object,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * Props applied to the [`Paper`](/material-ui/api/paper/) element.\n   * @default {}\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * Props applied to the [`Slide`](/material-ui/api/slide/) element.\n   */\n  SlideProps: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * The variant to use.\n   * @default 'temporary'\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default Drawer;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "PropTypes", "clsx", "integerPropType", "composeClasses", "useRtl", "Modal", "Slide", "Paper", "capitalize", "useTheme", "useDefaultProps", "styled", "rootShouldForwardProp", "getDrawerUtilityClass", "jsx", "_jsx", "overridesResolver", "props", "styles", "ownerState", "root", "variant", "docked", "modal", "useUtilityClasses", "classes", "anchor", "slots", "paper", "Drawer<PERSON><PERSON>", "name", "slot", "theme", "zIndex", "vars", "drawer", "DrawerDockedRoot", "shouldForwardProp", "skipVariantsResolver", "flex", "Drawer<PERSON><PERSON>", "overflowY", "display", "flexDirection", "height", "WebkitOverflowScrolling", "position", "top", "outline", "left", "right", "maxHeight", "bottom", "borderRight", "palette", "divider", "borderBottom", "borderLeft", "borderTop", "oppositeDirection", "isHorizontal", "indexOf", "getAnchor", "direction", "Drawer", "forwardRef", "inProps", "ref", "isRtl", "defaultTransitionDuration", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "anchorProp", "BackdropProps", "children", "className", "elevation", "hideBackdrop", "ModalProps", "BackdropPropsProp", "onClose", "open", "PaperProps", "SlideProps", "TransitionComponent", "transitionDuration", "other", "mounted", "useRef", "useEffect", "current", "anchorInvariant", "square", "sliding<PERSON><PERSON><PERSON>", "in", "timeout", "appear", "process", "env", "NODE_ENV", "propTypes", "oneOf", "object", "node", "string", "bool", "func", "sx", "oneOfType", "arrayOf", "number", "shape"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/Drawer/Drawer.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"BackdropProps\", \"children\", \"className\", \"elevation\", \"hideBackdrop\", \"ModalProps\", \"onClose\", \"open\", \"PaperProps\", \"SlideProps\", \"TransitionComponent\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport Modal from '../Modal';\nimport Slide from '../Slide';\nimport Paper from '../Paper';\nimport capitalize from '../utils/capitalize';\nimport useTheme from '../styles/useTheme';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { getDrawerUtilityClass } from './drawerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, (ownerState.variant === 'permanent' || ownerState.variant === 'persistent') && styles.docked, styles.modal];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    anchor,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    docked: [(variant === 'permanent' || variant === 'persistent') && 'docked'],\n    modal: ['modal'],\n    paper: ['paper', `paperAnchor${capitalize(anchor)}`, variant !== 'temporary' && `paperAnchorDocked${capitalize(anchor)}`]\n  };\n  return composeClasses(slots, getDrawerUtilityClass, classes);\n};\nconst DrawerRoot = styled(Modal, {\n  name: 'MuiDrawer',\n  slot: 'Root',\n  overridesResolver\n})(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.drawer\n}));\nconst DrawerDockedRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp,\n  name: 'MuiDrawer',\n  slot: 'Docked',\n  skipVariantsResolver: false,\n  overridesResolver\n})({\n  flex: '0 0 auto'\n});\nconst DrawerPaper = styled(Paper, {\n  name: 'MuiDrawer',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.paper, styles[`paperAnchor${capitalize(ownerState.anchor)}`], ownerState.variant !== 'temporary' && styles[`paperAnchorDocked${capitalize(ownerState.anchor)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  overflowY: 'auto',\n  display: 'flex',\n  flexDirection: 'column',\n  height: '100%',\n  flex: '1 0 auto',\n  zIndex: (theme.vars || theme).zIndex.drawer,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  // temporary style\n  position: 'fixed',\n  top: 0,\n  // We disable the focus ring for mouse, touch and keyboard users.\n  // At some point, it would be better to keep it for keyboard users.\n  // :focus-ring CSS pseudo-class will help.\n  outline: 0\n}, ownerState.anchor === 'left' && {\n  left: 0\n}, ownerState.anchor === 'top' && {\n  top: 0,\n  left: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'right' && {\n  right: 0\n}, ownerState.anchor === 'bottom' && {\n  top: 'auto',\n  left: 0,\n  bottom: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'left' && ownerState.variant !== 'temporary' && {\n  borderRight: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'top' && ownerState.variant !== 'temporary' && {\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'right' && ownerState.variant !== 'temporary' && {\n  borderLeft: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'bottom' && ownerState.variant !== 'temporary' && {\n  borderTop: `1px solid ${(theme.vars || theme).palette.divider}`\n}));\nconst oppositeDirection = {\n  left: 'right',\n  right: 'left',\n  top: 'down',\n  bottom: 'up'\n};\nexport function isHorizontal(anchor) {\n  return ['left', 'right'].indexOf(anchor) !== -1;\n}\nexport function getAnchor({\n  direction\n}, anchor) {\n  return direction === 'rtl' && isHorizontal(anchor) ? oppositeDirection[anchor] : anchor;\n}\n\n/**\n * The props of the [Modal](/material-ui/api/modal/) component are available\n * when `variant=\"temporary\"` is set.\n */\nconst Drawer = /*#__PURE__*/React.forwardRef(function Drawer(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDrawer'\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor: anchorProp = 'left',\n      BackdropProps,\n      children,\n      className,\n      elevation = 16,\n      hideBackdrop = false,\n      ModalProps: {\n        BackdropProps: BackdropPropsProp\n      } = {},\n      onClose,\n      open = false,\n      PaperProps = {},\n      SlideProps,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Slide,\n      transitionDuration = defaultTransitionDuration,\n      variant = 'temporary'\n    } = props,\n    ModalProps = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n\n  // Let's assume that the Drawer will always be rendered on user space.\n  // We use this state is order to skip the appear transition during the\n  // initial mount of the component.\n  const mounted = React.useRef(false);\n  React.useEffect(() => {\n    mounted.current = true;\n  }, []);\n  const anchorInvariant = getAnchor({\n    direction: isRtl ? 'rtl' : 'ltr'\n  }, anchorProp);\n  const anchor = anchorProp;\n  const ownerState = _extends({}, props, {\n    anchor,\n    elevation,\n    open,\n    variant\n  }, other);\n  const classes = useUtilityClasses(ownerState);\n  const drawer = /*#__PURE__*/_jsx(DrawerPaper, _extends({\n    elevation: variant === 'temporary' ? elevation : 0,\n    square: true\n  }, PaperProps, {\n    className: clsx(classes.paper, PaperProps.className),\n    ownerState: ownerState,\n    children: children\n  }));\n  if (variant === 'permanent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: drawer\n    }));\n  }\n  const slidingDrawer = /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    direction: oppositeDirection[anchorInvariant],\n    timeout: transitionDuration,\n    appear: mounted.current\n  }, SlideProps, {\n    children: drawer\n  }));\n  if (variant === 'persistent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: slidingDrawer\n    }));\n  }\n\n  // variant === temporary\n  return /*#__PURE__*/_jsx(DrawerRoot, _extends({\n    BackdropProps: _extends({}, BackdropProps, BackdropPropsProp, {\n      transitionDuration\n    }),\n    className: clsx(classes.root, classes.modal, className),\n    open: open,\n    ownerState: ownerState,\n    onClose: onClose,\n    hideBackdrop: hideBackdrop,\n    ref: ref\n  }, other, ModalProps, {\n    children: slidingDrawer\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Drawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Side from which the drawer will appear.\n   * @default 'left'\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * @ignore\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The elevation of the drawer.\n   * @default 16\n   */\n  elevation: integerPropType,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Props applied to the [`Modal`](/material-ui/api/modal/) element.\n   * @default {}\n   */\n  ModalProps: PropTypes.object,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * Props applied to the [`Paper`](/material-ui/api/paper/) element.\n   * @default {}\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * Props applied to the [`Slide`](/material-ui/api/slide/) element.\n   */\n  SlideProps: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * The variant to use.\n   * @default 'temporary'\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default Drawer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,eAAe,CAAC;EACjCC,UAAU,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,SAAS,CAAC;AACrN,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAC3C,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAE,CAACD,UAAU,CAACE,OAAO,KAAK,WAAW,IAAIF,UAAU,CAACE,OAAO,KAAK,YAAY,KAAKH,MAAM,CAACI,MAAM,EAAEJ,MAAM,CAACK,KAAK,CAAC;AAClI,CAAC;AACD,MAAMC,iBAAiB,GAAGL,UAAU,IAAI;EACtC,MAAM;IACJM,OAAO;IACPC,MAAM;IACNL;EACF,CAAC,GAAGF,UAAU;EACd,MAAMQ,KAAK,GAAG;IACZP,IAAI,EAAE,CAAC,MAAM,CAAC;IACdE,MAAM,EAAE,CAAC,CAACD,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,YAAY,KAAK,QAAQ,CAAC;IAC3EE,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBK,KAAK,EAAE,CAAC,OAAO,EAAE,cAAcpB,UAAU,CAACkB,MAAM,CAAC,EAAE,EAAEL,OAAO,KAAK,WAAW,IAAI,oBAAoBb,UAAU,CAACkB,MAAM,CAAC,EAAE;EAC1H,CAAC;EACD,OAAOvB,cAAc,CAACwB,KAAK,EAAEd,qBAAqB,EAAEY,OAAO,CAAC;AAC9D,CAAC;AACD,MAAMI,UAAU,GAAGlB,MAAM,CAACN,KAAK,EAAE;EAC/ByB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZf;AACF,CAAC,CAAC,CAAC,CAAC;EACFgB;AACF,CAAC,MAAM;EACLC,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACE;AACvC,CAAC,CAAC,CAAC;AACH,MAAMC,gBAAgB,GAAGzB,MAAM,CAAC,KAAK,EAAE;EACrC0B,iBAAiB,EAAEzB,qBAAqB;EACxCkB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,QAAQ;EACdO,oBAAoB,EAAE,KAAK;EAC3BtB;AACF,CAAC,CAAC,CAAC;EACDuB,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG7B,MAAM,CAACJ,KAAK,EAAE;EAChCuB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbf,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO,CAACC,MAAM,CAACU,KAAK,EAAEV,MAAM,CAAC,cAAcV,UAAU,CAACW,UAAU,CAACO,MAAM,CAAC,EAAE,CAAC,EAAEP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAIH,MAAM,CAAC,oBAAoBV,UAAU,CAACW,UAAU,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC;EACjL;AACF,CAAC,CAAC,CAAC,CAAC;EACFM,KAAK;EACLb;AACF,CAAC,KAAKvB,QAAQ,CAAC;EACb6C,SAAS,EAAE,MAAM;EACjBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBC,MAAM,EAAE,MAAM;EACdL,IAAI,EAAE,UAAU;EAChBN,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACE,MAAM;EAC3C;EACAU,uBAAuB,EAAE,OAAO;EAChC;EACAC,QAAQ,EAAE,OAAO;EACjBC,GAAG,EAAE,CAAC;EACN;EACA;EACA;EACAC,OAAO,EAAE;AACX,CAAC,EAAE7B,UAAU,CAACO,MAAM,KAAK,MAAM,IAAI;EACjCuB,IAAI,EAAE;AACR,CAAC,EAAE9B,UAAU,CAACO,MAAM,KAAK,KAAK,IAAI;EAChCqB,GAAG,EAAE,CAAC;EACNE,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRN,MAAM,EAAE,MAAM;EACdO,SAAS,EAAE;AACb,CAAC,EAAEhC,UAAU,CAACO,MAAM,KAAK,OAAO,IAAI;EAClCwB,KAAK,EAAE;AACT,CAAC,EAAE/B,UAAU,CAACO,MAAM,KAAK,QAAQ,IAAI;EACnCqB,GAAG,EAAE,MAAM;EACXE,IAAI,EAAE,CAAC;EACPG,MAAM,EAAE,CAAC;EACTF,KAAK,EAAE,CAAC;EACRN,MAAM,EAAE,MAAM;EACdO,SAAS,EAAE;AACb,CAAC,EAAEhC,UAAU,CAACO,MAAM,KAAK,MAAM,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACvEgC,WAAW,EAAE,aAAa,CAACrB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAO;AACjE,CAAC,EAAEpC,UAAU,CAACO,MAAM,KAAK,KAAK,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACtEmC,YAAY,EAAE,aAAa,CAACxB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAO;AAClE,CAAC,EAAEpC,UAAU,CAACO,MAAM,KAAK,OAAO,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACxEoC,UAAU,EAAE,aAAa,CAACzB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAO;AAChE,CAAC,EAAEpC,UAAU,CAACO,MAAM,KAAK,QAAQ,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACzEqC,SAAS,EAAE,aAAa,CAAC1B,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAO;AAC/D,CAAC,CAAC,CAAC;AACH,MAAMI,iBAAiB,GAAG;EACxBV,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,MAAM;EACbH,GAAG,EAAE,MAAM;EACXK,MAAM,EAAE;AACV,CAAC;AACD,OAAO,SAASQ,YAAYA,CAAClC,MAAM,EAAE;EACnC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAACmC,OAAO,CAACnC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjD;AACA,OAAO,SAASoC,SAASA,CAAC;EACxBC;AACF,CAAC,EAAErC,MAAM,EAAE;EACT,OAAOqC,SAAS,KAAK,KAAK,IAAIH,YAAY,CAAClC,MAAM,CAAC,GAAGiC,iBAAiB,CAACjC,MAAM,CAAC,GAAGA,MAAM;AACzF;;AAEA;AACA;AACA;AACA;AACA,MAAMsC,MAAM,GAAG,aAAajE,KAAK,CAACkE,UAAU,CAAC,SAASD,MAAMA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMlD,KAAK,GAAGP,eAAe,CAAC;IAC5BO,KAAK,EAAEiD,OAAO;IACdpC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAME,KAAK,GAAGvB,QAAQ,CAAC,CAAC;EACxB,MAAM2D,KAAK,GAAGhE,MAAM,CAAC,CAAC;EACtB,MAAMiE,yBAAyB,GAAG;IAChCC,KAAK,EAAEtC,KAAK,CAACuC,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAE1C,KAAK,CAACuC,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;MACFjD,MAAM,EAAEkD,UAAU,GAAG,MAAM;MAC3BC,aAAa;MACbC,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,EAAE;MACdC,YAAY,GAAG,KAAK;MACpBC,UAAU,EAAE;QACVL,aAAa,EAAEM;MACjB,CAAC,GAAG,CAAC,CAAC;MACNC,OAAO;MACPC,IAAI,GAAG,KAAK;MACZC,UAAU,GAAG,CAAC,CAAC;MACfC,UAAU;MACV;MACAC,mBAAmB,GAAGlF,KAAK;MAC3BmF,kBAAkB,GAAGpB,yBAAyB;MAC9ChD,OAAO,GAAG;IACZ,CAAC,GAAGJ,KAAK;IACTiE,UAAU,GAAGvF,6BAA6B,CAACsB,KAAK,CAACiE,UAAU,EAAErF,SAAS,CAAC;IACvE6F,KAAK,GAAG/F,6BAA6B,CAACsB,KAAK,EAAEnB,UAAU,CAAC;;EAE1D;EACA;EACA;EACA,MAAM6F,OAAO,GAAG5F,KAAK,CAAC6F,MAAM,CAAC,KAAK,CAAC;EACnC7F,KAAK,CAAC8F,SAAS,CAAC,MAAM;IACpBF,OAAO,CAACG,OAAO,GAAG,IAAI;EACxB,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,eAAe,GAAGjC,SAAS,CAAC;IAChCC,SAAS,EAAEK,KAAK,GAAG,KAAK,GAAG;EAC7B,CAAC,EAAEQ,UAAU,CAAC;EACd,MAAMlD,MAAM,GAAGkD,UAAU;EACzB,MAAMzD,UAAU,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCS,MAAM;IACNsD,SAAS;IACTK,IAAI;IACJhE;EACF,CAAC,EAAEqE,KAAK,CAAC;EACT,MAAMjE,OAAO,GAAGD,iBAAiB,CAACL,UAAU,CAAC;EAC7C,MAAMgB,MAAM,GAAG,aAAapB,IAAI,CAACyB,WAAW,EAAE5C,QAAQ,CAAC;IACrDoF,SAAS,EAAE3D,OAAO,KAAK,WAAW,GAAG2D,SAAS,GAAG,CAAC;IAClDgB,MAAM,EAAE;EACV,CAAC,EAAEV,UAAU,EAAE;IACbP,SAAS,EAAE9E,IAAI,CAACwB,OAAO,CAACG,KAAK,EAAE0D,UAAU,CAACP,SAAS,CAAC;IACpD5D,UAAU,EAAEA,UAAU;IACtB2D,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;EACH,IAAIzD,OAAO,KAAK,WAAW,EAAE;IAC3B,OAAO,aAAaN,IAAI,CAACqB,gBAAgB,EAAExC,QAAQ,CAAC;MAClDmF,SAAS,EAAE9E,IAAI,CAACwB,OAAO,CAACL,IAAI,EAAEK,OAAO,CAACH,MAAM,EAAEyD,SAAS,CAAC;MACxD5D,UAAU,EAAEA,UAAU;MACtBgD,GAAG,EAAEA;IACP,CAAC,EAAEuB,KAAK,EAAE;MACRZ,QAAQ,EAAE3C;IACZ,CAAC,CAAC,CAAC;EACL;EACA,MAAM8D,aAAa,GAAG,aAAalF,IAAI,CAACyE,mBAAmB,EAAE5F,QAAQ,CAAC;IACpEsG,EAAE,EAAEb,IAAI;IACRtB,SAAS,EAAEJ,iBAAiB,CAACoC,eAAe,CAAC;IAC7CI,OAAO,EAAEV,kBAAkB;IAC3BW,MAAM,EAAET,OAAO,CAACG;EAClB,CAAC,EAAEP,UAAU,EAAE;IACbT,QAAQ,EAAE3C;EACZ,CAAC,CAAC,CAAC;EACH,IAAId,OAAO,KAAK,YAAY,EAAE;IAC5B,OAAO,aAAaN,IAAI,CAACqB,gBAAgB,EAAExC,QAAQ,CAAC;MAClDmF,SAAS,EAAE9E,IAAI,CAACwB,OAAO,CAACL,IAAI,EAAEK,OAAO,CAACH,MAAM,EAAEyD,SAAS,CAAC;MACxD5D,UAAU,EAAEA,UAAU;MACtBgD,GAAG,EAAEA;IACP,CAAC,EAAEuB,KAAK,EAAE;MACRZ,QAAQ,EAAEmB;IACZ,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAO,aAAalF,IAAI,CAACc,UAAU,EAAEjC,QAAQ,CAAC;IAC5CiF,aAAa,EAAEjF,QAAQ,CAAC,CAAC,CAAC,EAAEiF,aAAa,EAAEM,iBAAiB,EAAE;MAC5DM;IACF,CAAC,CAAC;IACFV,SAAS,EAAE9E,IAAI,CAACwB,OAAO,CAACL,IAAI,EAAEK,OAAO,CAACF,KAAK,EAAEwD,SAAS,CAAC;IACvDM,IAAI,EAAEA,IAAI;IACVlE,UAAU,EAAEA,UAAU;IACtBiE,OAAO,EAAEA,OAAO;IAChBH,YAAY,EAAEA,YAAY;IAC1Bd,GAAG,EAAEA;EACP,CAAC,EAAEuB,KAAK,EAAER,UAAU,EAAE;IACpBJ,QAAQ,EAAEmB;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvC,MAAM,CAACwC,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE9E,MAAM,EAAE1B,SAAS,CAACyG,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D;AACF;AACA;EACE5B,aAAa,EAAE7E,SAAS,CAAC0G,MAAM;EAC/B;AACF;AACA;EACE5B,QAAQ,EAAE9E,SAAS,CAAC2G,IAAI;EACxB;AACF;AACA;EACElF,OAAO,EAAEzB,SAAS,CAAC0G,MAAM;EACzB;AACF;AACA;EACE3B,SAAS,EAAE/E,SAAS,CAAC4G,MAAM;EAC3B;AACF;AACA;AACA;EACE5B,SAAS,EAAE9E,eAAe;EAC1B;AACF;AACA;AACA;EACE+E,YAAY,EAAEjF,SAAS,CAAC6G,IAAI;EAC5B;AACF;AACA;AACA;EACE3B,UAAU,EAAElF,SAAS,CAAC0G,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;EACEtB,OAAO,EAAEpF,SAAS,CAAC8G,IAAI;EACvB;AACF;AACA;AACA;EACEzB,IAAI,EAAErF,SAAS,CAAC6G,IAAI;EACpB;AACF;AACA;AACA;EACEvB,UAAU,EAAEtF,SAAS,CAAC0G,MAAM;EAC5B;AACF;AACA;EACEnB,UAAU,EAAEvF,SAAS,CAAC0G,MAAM;EAC5B;AACF;AACA;EACEK,EAAE,EAAE/G,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,OAAO,CAACjH,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAAC8G,IAAI,EAAE9G,SAAS,CAAC0G,MAAM,EAAE1G,SAAS,CAAC6G,IAAI,CAAC,CAAC,CAAC,EAAE7G,SAAS,CAAC8G,IAAI,EAAE9G,SAAS,CAAC0G,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEjB,kBAAkB,EAAEzF,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACkH,MAAM,EAAElH,SAAS,CAACmH,KAAK,CAAC;IACzEf,MAAM,EAAEpG,SAAS,CAACkH,MAAM;IACxB5C,KAAK,EAAEtE,SAAS,CAACkH,MAAM;IACvBxC,IAAI,EAAE1E,SAAS,CAACkH;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;EACE7F,OAAO,EAAErB,SAAS,CAACyG,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAezC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}