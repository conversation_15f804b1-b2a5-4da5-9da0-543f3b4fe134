{"ast": null, "code": "let warnedOnce = false;\n\n// To remove in v6\nexport default function createStyles(styles) {\n  if (!warnedOnce) {\n    console.warn(['MUI: createStyles from @mui/material/styles is deprecated.', 'Please use @mui/styles/createStyles'].join('\\n'));\n    warnedOnce = true;\n  }\n  return styles;\n}", "map": {"version": 3, "names": ["warnedOnce", "createStyles", "styles", "console", "warn", "join"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/styles/createStyles.js"], "sourcesContent": ["let warnedOnce = false;\n\n// To remove in v6\nexport default function createStyles(styles) {\n  if (!warnedOnce) {\n    console.warn(['MUI: createStyles from @mui/material/styles is deprecated.', 'Please use @mui/styles/createStyles'].join('\\n'));\n    warnedOnce = true;\n  }\n  return styles;\n}"], "mappings": "AAAA,IAAIA,UAAU,GAAG,KAAK;;AAEtB;AACA,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAE;EAC3C,IAAI,CAACF,UAAU,EAAE;IACfG,OAAO,CAACC,IAAI,CAAC,CAAC,4DAA4D,EAAE,qCAAqC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9HL,UAAU,GAAG,IAAI;EACnB;EACA,OAAOE,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}