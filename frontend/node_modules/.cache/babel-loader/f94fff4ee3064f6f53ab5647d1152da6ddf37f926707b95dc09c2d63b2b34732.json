{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogActions', slot);\n}\nconst dialogActionsClasses = generateUtilityClasses('MuiDialogActions', ['root', 'spacing']);\nexport default dialogActionsClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getDialogActionsUtilityClass", "slot", "dialogActionsClasses"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/DialogActions/dialogActionsClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogActions', slot);\n}\nconst dialogActionsClasses = generateUtilityClasses('MuiDialogActions', ['root', 'spacing']);\nexport default dialogActionsClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGJ,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC5F,eAAeI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}