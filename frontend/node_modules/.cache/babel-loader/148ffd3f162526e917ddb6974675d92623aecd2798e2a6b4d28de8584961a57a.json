{"ast": null, "code": "import getWindow from \"./getWindow.js\";\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\nexport { isElement, isHTMLElement, isShadowRoot };", "map": {"version": 3, "names": ["getWindow", "isElement", "node", "OwnElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js"], "sourcesContent": ["import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIC,UAAU,GAAGH,SAAS,CAACE,IAAI,CAAC,CAACE,OAAO;EACxC,OAAOF,IAAI,YAAYC,UAAU,IAAID,IAAI,YAAYE,OAAO;AAC9D;AAEA,SAASC,aAAaA,CAACH,IAAI,EAAE;EAC3B,IAAIC,UAAU,GAAGH,SAAS,CAACE,IAAI,CAAC,CAACI,WAAW;EAC5C,OAAOJ,IAAI,YAAYC,UAAU,IAAID,IAAI,YAAYI,WAAW;AAClE;AAEA,SAASC,YAAYA,CAACL,IAAI,EAAE;EAC1B;EACA,IAAI,OAAOM,UAAU,KAAK,WAAW,EAAE;IACrC,OAAO,KAAK;EACd;EAEA,IAAIL,UAAU,GAAGH,SAAS,CAACE,IAAI,CAAC,CAACM,UAAU;EAC3C,OAAON,IAAI,YAAYC,UAAU,IAAID,IAAI,YAAYM,UAAU;AACjE;AAEA,SAASP,SAAS,EAAEI,aAAa,EAAEE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}