{"ast": null, "code": "/**\n * Audio service for handling microphone input and audio processing\n * Converts audio to protocol-compliant format (mulaw/8000)\n */\n\nclass AudioService {\n  constructor() {\n    this.mediaStream = null;\n    this.mediaRecorder = null;\n    this.audioContext = null;\n    this.analyserNode = null;\n    this.scriptProcessor = null;\n    this.callbacks = {};\n    this._isRecording = false;\n    this.volumeMonitorInterval = null;\n    // Protocol configuration (mulaw/8000)\n    this.PROTOCOL_SAMPLE_RATE = 8000;\n    this.PROTOCOL_BIT_DEPTH = 8;\n    this.PROTOCOL_CHANNELS = 1;\n    this.CHUNK_SIZE = 160;\n  }\n  // 20ms at 8000Hz\n\n  setCallbacks(callbacks) {\n    this.callbacks = callbacks;\n  }\n  async initializeAudio() {\n    try {\n      // Request microphone access\n      this.mediaStream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          sampleRate: 44100,\n          // Start with high quality, we'll downsample\n          channelCount: 1,\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        }\n      });\n\n      // Initialize audio context\n      this.audioContext = new (window.AudioContext || window.webkitAudioContext)({\n        sampleRate: 44100\n      });\n\n      // Create analyzer for volume monitoring\n      this.analyserNode = this.audioContext.createAnalyser();\n      this.analyserNode.fftSize = 256;\n\n      // Create script processor for real-time audio processing\n      this.scriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);\n\n      // Connect audio processing chain\n      const source = this.audioContext.createMediaStreamSource(this.mediaStream);\n      source.connect(this.analyserNode);\n      this.analyserNode.connect(this.scriptProcessor);\n      this.scriptProcessor.connect(this.audioContext.destination);\n\n      // Set up audio processing\n      this.scriptProcessor.onaudioprocess = event => {\n        if (this.isRecording) {\n          this.processAudioBuffer(event.inputBuffer);\n        }\n      };\n      console.log('Audio initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize audio:', error);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(new Error('Failed to access microphone'));\n      }\n      throw error;\n    }\n  }\n  processAudioBuffer(inputBuffer) {\n    try {\n      // Get audio data from input buffer\n      const inputData = inputBuffer.getChannelData(0);\n\n      // Downsample to 8000Hz\n      const downsampledData = this.downsampleTo8kHz(inputData, inputBuffer.sampleRate);\n\n      // Convert to 16-bit PCM\n      const pcmData = this.floatToPCM16(downsampledData);\n\n      // Convert to mulaw\n      const mulawData = this.pcmToMulaw(pcmData);\n\n      // Send in chunks\n      this.sendAudioChunks(mulawData);\n    } catch (error) {\n      console.error('Error processing audio buffer:', error);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(new Error('Audio processing failed'));\n      }\n    }\n  }\n  downsampleTo8kHz(inputData, inputSampleRate) {\n    if (inputSampleRate === this.PROTOCOL_SAMPLE_RATE) {\n      return inputData;\n    }\n    const ratio = inputSampleRate / this.PROTOCOL_SAMPLE_RATE;\n    const outputLength = Math.floor(inputData.length / ratio);\n    const outputData = new Float32Array(outputLength);\n    for (let i = 0; i < outputLength; i++) {\n      const inputIndex = Math.floor(i * ratio);\n      outputData[i] = inputData[inputIndex];\n    }\n    return outputData;\n  }\n  floatToPCM16(inputData) {\n    const outputData = new Int16Array(inputData.length);\n    for (let i = 0; i < inputData.length; i++) {\n      // Clamp and convert to 16-bit PCM\n      const sample = Math.max(-1, Math.min(1, inputData[i]));\n      outputData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\n    }\n    return outputData;\n  }\n  pcmToMulaw(pcmData) {\n    const mulawData = new Uint8Array(pcmData.length);\n    for (let i = 0; i < pcmData.length; i++) {\n      mulawData[i] = this.linearToMulaw(pcmData[i]);\n    }\n    return mulawData;\n  }\n  linearToMulaw(sample) {\n    // Mulaw compression algorithm\n    const BIAS = 0x84;\n    const CLIP = 32635;\n    let sign = sample >> 8 & 0x80;\n    if (sign !== 0) {\n      sample = -sample;\n    }\n    if (sample > CLIP) {\n      sample = CLIP;\n    }\n    sample += BIAS;\n    let exponent = 0;\n    let mantissa = 0;\n    if (sample >= 256) {\n      exponent = 1;\n      sample >>= 1;\n      while (sample >= 256 && exponent < 7) {\n        exponent++;\n        sample >>= 1;\n      }\n    }\n    mantissa = sample >> 4 & 0x0F;\n    return ~(sign | exponent << 4 | mantissa);\n  }\n  sendAudioChunks(mulawData) {\n    // Send data in 20ms chunks (160 samples at 8kHz)\n    for (let i = 0; i < mulawData.length; i += this.CHUNK_SIZE) {\n      const chunk = mulawData.slice(i, i + this.CHUNK_SIZE);\n      if (this.callbacks.onAudioData) {\n        this.callbacks.onAudioData(chunk.buffer);\n      }\n    }\n  }\n  startRecording() {\n    if (!this.audioContext || !this.mediaStream) {\n      throw new Error('Audio not initialized');\n    }\n    if (this.audioContext.state === 'suspended') {\n      this.audioContext.resume();\n    }\n    this.isRecording = true;\n    this.startVolumeMonitoring();\n    if (this.callbacks.onStartRecording) {\n      this.callbacks.onStartRecording();\n    }\n    console.log('Recording started');\n  }\n  stopRecording() {\n    this.isRecording = false;\n    this.stopVolumeMonitoring();\n    if (this.callbacks.onStopRecording) {\n      this.callbacks.onStopRecording();\n    }\n    console.log('Recording stopped');\n  }\n  startVolumeMonitoring() {\n    if (!this.analyserNode) return;\n    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);\n    const updateVolume = () => {\n      if (!this.isRecording || !this.analyserNode) return;\n      this.analyserNode.getByteFrequencyData(dataArray);\n\n      // Calculate volume level (0-100)\n      let sum = 0;\n      for (let i = 0; i < dataArray.length; i++) {\n        sum += dataArray[i];\n      }\n      const average = sum / dataArray.length;\n      const volumeLevel = Math.round(average / 255 * 100);\n      if (this.callbacks.onVolumeLevel) {\n        this.callbacks.onVolumeLevel(volumeLevel);\n      }\n    };\n    this.volumeMonitorInterval = window.setInterval(updateVolume, 100);\n  }\n  stopVolumeMonitoring() {\n    if (this.volumeMonitorInterval) {\n      clearInterval(this.volumeMonitorInterval);\n      this.volumeMonitorInterval = null;\n    }\n  }\n  toggleMute() {\n    if (!this.mediaStream) return false;\n    const audioTracks = this.mediaStream.getAudioTracks();\n    if (audioTracks.length > 0) {\n      const currentState = audioTracks[0].enabled;\n      audioTracks[0].enabled = !currentState;\n      return !currentState; // Return new muted state\n    }\n    return false;\n  }\n  isMuted() {\n    if (!this.mediaStream) return true;\n    const audioTracks = this.mediaStream.getAudioTracks();\n    return audioTracks.length === 0 || !audioTracks[0].enabled;\n  }\n  isRecording() {\n    return this.isRecording;\n  }\n  getVolumeLevel() {\n    if (!this.analyserNode) return 0;\n    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);\n    this.analyserNode.getByteFrequencyData(dataArray);\n    let sum = 0;\n    for (let i = 0; i < dataArray.length; i++) {\n      sum += dataArray[i];\n    }\n    return Math.round(sum / dataArray.length / 255 * 100);\n  }\n  cleanup() {\n    this.stopRecording();\n    this.stopVolumeMonitoring();\n    if (this.scriptProcessor) {\n      this.scriptProcessor.disconnect();\n      this.scriptProcessor = null;\n    }\n    if (this.analyserNode) {\n      this.analyserNode.disconnect();\n      this.analyserNode = null;\n    }\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close();\n      this.audioContext = null;\n    }\n    if (this.mediaStream) {\n      this.mediaStream.getTracks().forEach(track => track.stop());\n      this.mediaStream = null;\n    }\n    console.log('Audio service cleaned up');\n  }\n\n  // Utility methods for testing\n  async testMicrophone() {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      stream.getTracks().forEach(track => track.stop());\n      return true;\n    } catch (error) {\n      console.error('Microphone test failed:', error);\n      return false;\n    }\n  }\n  async getAudioDevices() {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      return devices.filter(device => device.kind === 'audioinput');\n    } catch (error) {\n      console.error('Failed to get audio devices:', error);\n      return [];\n    }\n  }\n}\nexport const audioService = new AudioService();", "map": {"version": 3, "names": ["AudioService", "constructor", "mediaStream", "mediaRecorder", "audioContext", "analyserNode", "scriptProcessor", "callbacks", "_isRecording", "volumeMonitorInterval", "PROTOCOL_SAMPLE_RATE", "PROTOCOL_BIT_DEPTH", "PROTOCOL_CHANNELS", "CHUNK_SIZE", "setCallbacks", "initializeAudio", "navigator", "mediaDevices", "getUserMedia", "audio", "sampleRate", "channelCount", "echoCancellation", "noiseSuppression", "autoGainControl", "window", "AudioContext", "webkitAudioContext", "create<PERSON><PERSON>yser", "fftSize", "createScriptProcessor", "source", "createMediaStreamSource", "connect", "destination", "onaudioprocess", "event", "isRecording", "processAudioBuffer", "inputBuffer", "console", "log", "error", "onError", "Error", "inputData", "getChannelData", "downsampledData", "downsampleTo8kHz", "pcmData", "floatToPCM16", "mulawData", "pcmToMulaw", "sendAudioChunks", "inputSampleRate", "ratio", "outputLength", "Math", "floor", "length", "outputData", "Float32Array", "i", "inputIndex", "Int16Array", "sample", "max", "min", "Uint8Array", "linearToMulaw", "BIAS", "CLIP", "sign", "exponent", "mantissa", "chunk", "slice", "onAudioData", "buffer", "startRecording", "state", "resume", "startVolumeMonitoring", "onStartRecording", "stopRecording", "stopVolumeMonitoring", "onStopRecording", "dataArray", "frequencyBinCount", "updateVolume", "getByteFrequencyData", "sum", "average", "volumeLevel", "round", "onVolumeLevel", "setInterval", "clearInterval", "toggleMute", "audioTracks", "getAudioTracks", "currentState", "enabled", "isMuted", "getVolumeLevel", "cleanup", "disconnect", "close", "getTracks", "for<PERSON>ach", "track", "stop", "testMicrophone", "stream", "getAudioDevices", "devices", "enumerateDevices", "filter", "device", "kind", "audioService"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/services/audioService.ts"], "sourcesContent": ["/**\n * Audio service for handling microphone input and audio processing\n * Converts audio to protocol-compliant format (mulaw/8000)\n */\n\nexport interface AudioConfig {\n  sampleRate: number;\n  bitDepth: number;\n  channels: number;\n  chunkDuration: number; // milliseconds\n}\n\nexport interface AudioServiceCallbacks {\n  onAudioData?: (audioData: ArrayBuffer) => void;\n  onError?: (error: Error) => void;\n  onStartRecording?: () => void;\n  onStopRecording?: () => void;\n  onVolumeLevel?: (level: number) => void;\n}\n\nclass AudioService {\n  private mediaStream: MediaStream | null = null;\n  private mediaRecorder: MediaRecorder | null = null;\n  private audioContext: AudioContext | null = null;\n  private analyserNode: AnalyserNode | null = null;\n  private scriptProcessor: ScriptProcessorNode | null = null;\n  private callbacks: AudioServiceCallbacks = {};\n  private _isRecording = false;\n  private volumeMonitorInterval: number | null = null;\n\n  // Protocol configuration (mulaw/8000)\n  private readonly PROTOCOL_SAMPLE_RATE = 8000;\n  private readonly PROTOCOL_BIT_DEPTH = 8;\n  private readonly PROTOCOL_CHANNELS = 1;\n  private readonly CHUNK_SIZE = 160; // 20ms at 8000Hz\n\n  public setCallbacks(callbacks: AudioServiceCallbacks) {\n    this.callbacks = callbacks;\n  }\n\n  public async initializeAudio(): Promise<void> {\n    try {\n      // Request microphone access\n      this.mediaStream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          sampleRate: 44100, // Start with high quality, we'll downsample\n          channelCount: 1,\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        }\n      });\n\n      // Initialize audio context\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({\n        sampleRate: 44100\n      });\n\n      // Create analyzer for volume monitoring\n      this.analyserNode = this.audioContext.createAnalyser();\n      this.analyserNode.fftSize = 256;\n\n      // Create script processor for real-time audio processing\n      this.scriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);\n      \n      // Connect audio processing chain\n      const source = this.audioContext.createMediaStreamSource(this.mediaStream);\n      source.connect(this.analyserNode);\n      this.analyserNode.connect(this.scriptProcessor);\n      this.scriptProcessor.connect(this.audioContext.destination);\n\n      // Set up audio processing\n      this.scriptProcessor.onaudioprocess = (event) => {\n        if (this.isRecording) {\n          this.processAudioBuffer(event.inputBuffer);\n        }\n      };\n\n      console.log('Audio initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize audio:', error);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(new Error('Failed to access microphone'));\n      }\n      throw error;\n    }\n  }\n\n  private processAudioBuffer(inputBuffer: AudioBuffer) {\n    try {\n      // Get audio data from input buffer\n      const inputData = inputBuffer.getChannelData(0);\n      \n      // Downsample to 8000Hz\n      const downsampledData = this.downsampleTo8kHz(inputData, inputBuffer.sampleRate);\n      \n      // Convert to 16-bit PCM\n      const pcmData = this.floatToPCM16(downsampledData);\n      \n      // Convert to mulaw\n      const mulawData = this.pcmToMulaw(pcmData);\n      \n      // Send in chunks\n      this.sendAudioChunks(mulawData);\n      \n    } catch (error) {\n      console.error('Error processing audio buffer:', error);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(new Error('Audio processing failed'));\n      }\n    }\n  }\n\n  private downsampleTo8kHz(inputData: Float32Array, inputSampleRate: number): Float32Array {\n    if (inputSampleRate === this.PROTOCOL_SAMPLE_RATE) {\n      return inputData;\n    }\n\n    const ratio = inputSampleRate / this.PROTOCOL_SAMPLE_RATE;\n    const outputLength = Math.floor(inputData.length / ratio);\n    const outputData = new Float32Array(outputLength);\n\n    for (let i = 0; i < outputLength; i++) {\n      const inputIndex = Math.floor(i * ratio);\n      outputData[i] = inputData[inputIndex];\n    }\n\n    return outputData;\n  }\n\n  private floatToPCM16(inputData: Float32Array): Int16Array {\n    const outputData = new Int16Array(inputData.length);\n    \n    for (let i = 0; i < inputData.length; i++) {\n      // Clamp and convert to 16-bit PCM\n      const sample = Math.max(-1, Math.min(1, inputData[i]));\n      outputData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\n    }\n    \n    return outputData;\n  }\n\n  private pcmToMulaw(pcmData: Int16Array): Uint8Array {\n    const mulawData = new Uint8Array(pcmData.length);\n    \n    for (let i = 0; i < pcmData.length; i++) {\n      mulawData[i] = this.linearToMulaw(pcmData[i]);\n    }\n    \n    return mulawData;\n  }\n\n  private linearToMulaw(sample: number): number {\n    // Mulaw compression algorithm\n    const BIAS = 0x84;\n    const CLIP = 32635;\n    \n    let sign = (sample >> 8) & 0x80;\n    if (sign !== 0) {\n      sample = -sample;\n    }\n    \n    if (sample > CLIP) {\n      sample = CLIP;\n    }\n    \n    sample += BIAS;\n    \n    let exponent = 0;\n    let mantissa = 0;\n    \n    if (sample >= 256) {\n      exponent = 1;\n      sample >>= 1;\n      while (sample >= 256 && exponent < 7) {\n        exponent++;\n        sample >>= 1;\n      }\n    }\n    \n    mantissa = (sample >> 4) & 0x0F;\n    \n    return ~(sign | (exponent << 4) | mantissa);\n  }\n\n  private sendAudioChunks(mulawData: Uint8Array) {\n    // Send data in 20ms chunks (160 samples at 8kHz)\n    for (let i = 0; i < mulawData.length; i += this.CHUNK_SIZE) {\n      const chunk = mulawData.slice(i, i + this.CHUNK_SIZE);\n      \n      if (this.callbacks.onAudioData) {\n        this.callbacks.onAudioData(chunk.buffer);\n      }\n    }\n  }\n\n  public startRecording(): void {\n    if (!this.audioContext || !this.mediaStream) {\n      throw new Error('Audio not initialized');\n    }\n\n    if (this.audioContext.state === 'suspended') {\n      this.audioContext.resume();\n    }\n\n    this.isRecording = true;\n    this.startVolumeMonitoring();\n\n    if (this.callbacks.onStartRecording) {\n      this.callbacks.onStartRecording();\n    }\n\n    console.log('Recording started');\n  }\n\n  public stopRecording(): void {\n    this.isRecording = false;\n    this.stopVolumeMonitoring();\n\n    if (this.callbacks.onStopRecording) {\n      this.callbacks.onStopRecording();\n    }\n\n    console.log('Recording stopped');\n  }\n\n  private startVolumeMonitoring() {\n    if (!this.analyserNode) return;\n\n    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);\n    \n    const updateVolume = () => {\n      if (!this.isRecording || !this.analyserNode) return;\n      \n      this.analyserNode.getByteFrequencyData(dataArray);\n      \n      // Calculate volume level (0-100)\n      let sum = 0;\n      for (let i = 0; i < dataArray.length; i++) {\n        sum += dataArray[i];\n      }\n      const average = sum / dataArray.length;\n      const volumeLevel = Math.round((average / 255) * 100);\n      \n      if (this.callbacks.onVolumeLevel) {\n        this.callbacks.onVolumeLevel(volumeLevel);\n      }\n    };\n\n    this.volumeMonitorInterval = window.setInterval(updateVolume, 100);\n  }\n\n  private stopVolumeMonitoring() {\n    if (this.volumeMonitorInterval) {\n      clearInterval(this.volumeMonitorInterval);\n      this.volumeMonitorInterval = null;\n    }\n  }\n\n  public toggleMute(): boolean {\n    if (!this.mediaStream) return false;\n\n    const audioTracks = this.mediaStream.getAudioTracks();\n    if (audioTracks.length > 0) {\n      const currentState = audioTracks[0].enabled;\n      audioTracks[0].enabled = !currentState;\n      return !currentState; // Return new muted state\n    }\n    \n    return false;\n  }\n\n  public isMuted(): boolean {\n    if (!this.mediaStream) return true;\n\n    const audioTracks = this.mediaStream.getAudioTracks();\n    return audioTracks.length === 0 || !audioTracks[0].enabled;\n  }\n\n  public isRecording(): boolean {\n    return this.isRecording;\n  }\n\n  public getVolumeLevel(): number {\n    if (!this.analyserNode) return 0;\n\n    const dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);\n    this.analyserNode.getByteFrequencyData(dataArray);\n    \n    let sum = 0;\n    for (let i = 0; i < dataArray.length; i++) {\n      sum += dataArray[i];\n    }\n    \n    return Math.round((sum / dataArray.length / 255) * 100);\n  }\n\n  public cleanup(): void {\n    this.stopRecording();\n    this.stopVolumeMonitoring();\n\n    if (this.scriptProcessor) {\n      this.scriptProcessor.disconnect();\n      this.scriptProcessor = null;\n    }\n\n    if (this.analyserNode) {\n      this.analyserNode.disconnect();\n      this.analyserNode = null;\n    }\n\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close();\n      this.audioContext = null;\n    }\n\n    if (this.mediaStream) {\n      this.mediaStream.getTracks().forEach(track => track.stop());\n      this.mediaStream = null;\n    }\n\n    console.log('Audio service cleaned up');\n  }\n\n  // Utility methods for testing\n  public async testMicrophone(): Promise<boolean> {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      stream.getTracks().forEach(track => track.stop());\n      return true;\n    } catch (error) {\n      console.error('Microphone test failed:', error);\n      return false;\n    }\n  }\n\n  public async getAudioDevices(): Promise<MediaDeviceInfo[]> {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      return devices.filter(device => device.kind === 'audioinput');\n    } catch (error) {\n      console.error('Failed to get audio devices:', error);\n      return [];\n    }\n  }\n}\n\nexport const audioService = new AudioService();"], "mappings": "AAAA;AACA;AACA;AACA;;AAiBA,MAAMA,YAAY,CAAC;EAAAC,YAAA;IAAA,KACTC,WAAW,GAAuB,IAAI;IAAA,KACtCC,aAAa,GAAyB,IAAI;IAAA,KAC1CC,YAAY,GAAwB,IAAI;IAAA,KACxCC,YAAY,GAAwB,IAAI;IAAA,KACxCC,eAAe,GAA+B,IAAI;IAAA,KAClDC,SAAS,GAA0B,CAAC,CAAC;IAAA,KACrCC,YAAY,GAAG,KAAK;IAAA,KACpBC,qBAAqB,GAAkB,IAAI;IAEnD;IAAA,KACiBC,oBAAoB,GAAG,IAAI;IAAA,KAC3BC,kBAAkB,GAAG,CAAC;IAAA,KACtBC,iBAAiB,GAAG,CAAC;IAAA,KACrBC,UAAU,GAAG,GAAG;EAAA;EAAE;;EAE5BC,YAAYA,CAACP,SAAgC,EAAE;IACpD,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC5B;EAEA,MAAaQ,eAAeA,CAAA,EAAkB;IAC5C,IAAI;MACF;MACA,IAAI,CAACb,WAAW,GAAG,MAAMc,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAC3DC,KAAK,EAAE;UACLC,UAAU,EAAE,KAAK;UAAE;UACnBC,YAAY,EAAE,CAAC;UACfC,gBAAgB,EAAE,IAAI;UACtBC,gBAAgB,EAAE,IAAI;UACtBC,eAAe,EAAE;QACnB;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACpB,YAAY,GAAG,KAAKqB,MAAM,CAACC,YAAY,IAAKD,MAAM,CAASE,kBAAkB,EAAE;QAClFP,UAAU,EAAE;MACd,CAAC,CAAC;;MAEF;MACA,IAAI,CAACf,YAAY,GAAG,IAAI,CAACD,YAAY,CAACwB,cAAc,CAAC,CAAC;MACtD,IAAI,CAACvB,YAAY,CAACwB,OAAO,GAAG,GAAG;;MAE/B;MACA,IAAI,CAACvB,eAAe,GAAG,IAAI,CAACF,YAAY,CAAC0B,qBAAqB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;;MAE1E;MACA,MAAMC,MAAM,GAAG,IAAI,CAAC3B,YAAY,CAAC4B,uBAAuB,CAAC,IAAI,CAAC9B,WAAW,CAAC;MAC1E6B,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC5B,YAAY,CAAC;MACjC,IAAI,CAACA,YAAY,CAAC4B,OAAO,CAAC,IAAI,CAAC3B,eAAe,CAAC;MAC/C,IAAI,CAACA,eAAe,CAAC2B,OAAO,CAAC,IAAI,CAAC7B,YAAY,CAAC8B,WAAW,CAAC;;MAE3D;MACA,IAAI,CAAC5B,eAAe,CAAC6B,cAAc,GAAIC,KAAK,IAAK;QAC/C,IAAI,IAAI,CAACC,WAAW,EAAE;UACpB,IAAI,CAACC,kBAAkB,CAACF,KAAK,CAACG,WAAW,CAAC;QAC5C;MACF,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,IAAI,CAACnC,SAAS,CAACoC,OAAO,EAAE;QAC1B,IAAI,CAACpC,SAAS,CAACoC,OAAO,CAAC,IAAIC,KAAK,CAAC,6BAA6B,CAAC,CAAC;MAClE;MACA,MAAMF,KAAK;IACb;EACF;EAEQJ,kBAAkBA,CAACC,WAAwB,EAAE;IACnD,IAAI;MACF;MACA,MAAMM,SAAS,GAAGN,WAAW,CAACO,cAAc,CAAC,CAAC,CAAC;;MAE/C;MACA,MAAMC,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAACH,SAAS,EAAEN,WAAW,CAACnB,UAAU,CAAC;;MAEhF;MACA,MAAM6B,OAAO,GAAG,IAAI,CAACC,YAAY,CAACH,eAAe,CAAC;;MAElD;MACA,MAAMI,SAAS,GAAG,IAAI,CAACC,UAAU,CAACH,OAAO,CAAC;;MAE1C;MACA,IAAI,CAACI,eAAe,CAACF,SAAS,CAAC;IAEjC,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,IAAI,CAACnC,SAAS,CAACoC,OAAO,EAAE;QAC1B,IAAI,CAACpC,SAAS,CAACoC,OAAO,CAAC,IAAIC,KAAK,CAAC,yBAAyB,CAAC,CAAC;MAC9D;IACF;EACF;EAEQI,gBAAgBA,CAACH,SAAuB,EAAES,eAAuB,EAAgB;IACvF,IAAIA,eAAe,KAAK,IAAI,CAAC5C,oBAAoB,EAAE;MACjD,OAAOmC,SAAS;IAClB;IAEA,MAAMU,KAAK,GAAGD,eAAe,GAAG,IAAI,CAAC5C,oBAAoB;IACzD,MAAM8C,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACb,SAAS,CAACc,MAAM,GAAGJ,KAAK,CAAC;IACzD,MAAMK,UAAU,GAAG,IAAIC,YAAY,CAACL,YAAY,CAAC;IAEjD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,YAAY,EAAEM,CAAC,EAAE,EAAE;MACrC,MAAMC,UAAU,GAAGN,IAAI,CAACC,KAAK,CAACI,CAAC,GAAGP,KAAK,CAAC;MACxCK,UAAU,CAACE,CAAC,CAAC,GAAGjB,SAAS,CAACkB,UAAU,CAAC;IACvC;IAEA,OAAOH,UAAU;EACnB;EAEQV,YAAYA,CAACL,SAAuB,EAAc;IACxD,MAAMe,UAAU,GAAG,IAAII,UAAU,CAACnB,SAAS,CAACc,MAAM,CAAC;IAEnD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,SAAS,CAACc,MAAM,EAAEG,CAAC,EAAE,EAAE;MACzC;MACA,MAAMG,MAAM,GAAGR,IAAI,CAACS,GAAG,CAAC,CAAC,CAAC,EAAET,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEtB,SAAS,CAACiB,CAAC,CAAC,CAAC,CAAC;MACtDF,UAAU,CAACE,CAAC,CAAC,GAAGG,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,MAAM,GAAGA,MAAM,GAAG,MAAM;IAChE;IAEA,OAAOL,UAAU;EACnB;EAEQR,UAAUA,CAACH,OAAmB,EAAc;IAClD,MAAME,SAAS,GAAG,IAAIiB,UAAU,CAACnB,OAAO,CAACU,MAAM,CAAC;IAEhD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,OAAO,CAACU,MAAM,EAAEG,CAAC,EAAE,EAAE;MACvCX,SAAS,CAACW,CAAC,CAAC,GAAG,IAAI,CAACO,aAAa,CAACpB,OAAO,CAACa,CAAC,CAAC,CAAC;IAC/C;IAEA,OAAOX,SAAS;EAClB;EAEQkB,aAAaA,CAACJ,MAAc,EAAU;IAC5C;IACA,MAAMK,IAAI,GAAG,IAAI;IACjB,MAAMC,IAAI,GAAG,KAAK;IAElB,IAAIC,IAAI,GAAIP,MAAM,IAAI,CAAC,GAAI,IAAI;IAC/B,IAAIO,IAAI,KAAK,CAAC,EAAE;MACdP,MAAM,GAAG,CAACA,MAAM;IAClB;IAEA,IAAIA,MAAM,GAAGM,IAAI,EAAE;MACjBN,MAAM,GAAGM,IAAI;IACf;IAEAN,MAAM,IAAIK,IAAI;IAEd,IAAIG,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAEhB,IAAIT,MAAM,IAAI,GAAG,EAAE;MACjBQ,QAAQ,GAAG,CAAC;MACZR,MAAM,KAAK,CAAC;MACZ,OAAOA,MAAM,IAAI,GAAG,IAAIQ,QAAQ,GAAG,CAAC,EAAE;QACpCA,QAAQ,EAAE;QACVR,MAAM,KAAK,CAAC;MACd;IACF;IAEAS,QAAQ,GAAIT,MAAM,IAAI,CAAC,GAAI,IAAI;IAE/B,OAAO,EAAEO,IAAI,GAAIC,QAAQ,IAAI,CAAE,GAAGC,QAAQ,CAAC;EAC7C;EAEQrB,eAAeA,CAACF,SAAqB,EAAE;IAC7C;IACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,CAACQ,MAAM,EAAEG,CAAC,IAAI,IAAI,CAACjD,UAAU,EAAE;MAC1D,MAAM8D,KAAK,GAAGxB,SAAS,CAACyB,KAAK,CAACd,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjD,UAAU,CAAC;MAErD,IAAI,IAAI,CAACN,SAAS,CAACsE,WAAW,EAAE;QAC9B,IAAI,CAACtE,SAAS,CAACsE,WAAW,CAACF,KAAK,CAACG,MAAM,CAAC;MAC1C;IACF;EACF;EAEOC,cAAcA,CAAA,EAAS;IAC5B,IAAI,CAAC,IAAI,CAAC3E,YAAY,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;MAC3C,MAAM,IAAI0C,KAAK,CAAC,uBAAuB,CAAC;IAC1C;IAEA,IAAI,IAAI,CAACxC,YAAY,CAAC4E,KAAK,KAAK,WAAW,EAAE;MAC3C,IAAI,CAAC5E,YAAY,CAAC6E,MAAM,CAAC,CAAC;IAC5B;IAEA,IAAI,CAAC5C,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC6C,qBAAqB,CAAC,CAAC;IAE5B,IAAI,IAAI,CAAC3E,SAAS,CAAC4E,gBAAgB,EAAE;MACnC,IAAI,CAAC5E,SAAS,CAAC4E,gBAAgB,CAAC,CAAC;IACnC;IAEA3C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClC;EAEO2C,aAAaA,CAAA,EAAS;IAC3B,IAAI,CAAC/C,WAAW,GAAG,KAAK;IACxB,IAAI,CAACgD,oBAAoB,CAAC,CAAC;IAE3B,IAAI,IAAI,CAAC9E,SAAS,CAAC+E,eAAe,EAAE;MAClC,IAAI,CAAC/E,SAAS,CAAC+E,eAAe,CAAC,CAAC;IAClC;IAEA9C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClC;EAEQyC,qBAAqBA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAAC7E,YAAY,EAAE;IAExB,MAAMkF,SAAS,GAAG,IAAInB,UAAU,CAAC,IAAI,CAAC/D,YAAY,CAACmF,iBAAiB,CAAC;IAErE,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAAC,IAAI,CAACpD,WAAW,IAAI,CAAC,IAAI,CAAChC,YAAY,EAAE;MAE7C,IAAI,CAACA,YAAY,CAACqF,oBAAoB,CAACH,SAAS,CAAC;;MAEjD;MACA,IAAII,GAAG,GAAG,CAAC;MACX,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,SAAS,CAAC5B,MAAM,EAAEG,CAAC,EAAE,EAAE;QACzC6B,GAAG,IAAIJ,SAAS,CAACzB,CAAC,CAAC;MACrB;MACA,MAAM8B,OAAO,GAAGD,GAAG,GAAGJ,SAAS,CAAC5B,MAAM;MACtC,MAAMkC,WAAW,GAAGpC,IAAI,CAACqC,KAAK,CAAEF,OAAO,GAAG,GAAG,GAAI,GAAG,CAAC;MAErD,IAAI,IAAI,CAACrF,SAAS,CAACwF,aAAa,EAAE;QAChC,IAAI,CAACxF,SAAS,CAACwF,aAAa,CAACF,WAAW,CAAC;MAC3C;IACF,CAAC;IAED,IAAI,CAACpF,qBAAqB,GAAGgB,MAAM,CAACuE,WAAW,CAACP,YAAY,EAAE,GAAG,CAAC;EACpE;EAEQJ,oBAAoBA,CAAA,EAAG;IAC7B,IAAI,IAAI,CAAC5E,qBAAqB,EAAE;MAC9BwF,aAAa,CAAC,IAAI,CAACxF,qBAAqB,CAAC;MACzC,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACnC;EACF;EAEOyF,UAAUA,CAAA,EAAY;IAC3B,IAAI,CAAC,IAAI,CAAChG,WAAW,EAAE,OAAO,KAAK;IAEnC,MAAMiG,WAAW,GAAG,IAAI,CAACjG,WAAW,CAACkG,cAAc,CAAC,CAAC;IACrD,IAAID,WAAW,CAACxC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAM0C,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC,CAACG,OAAO;MAC3CH,WAAW,CAAC,CAAC,CAAC,CAACG,OAAO,GAAG,CAACD,YAAY;MACtC,OAAO,CAACA,YAAY,CAAC,CAAC;IACxB;IAEA,OAAO,KAAK;EACd;EAEOE,OAAOA,CAAA,EAAY;IACxB,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE,OAAO,IAAI;IAElC,MAAMiG,WAAW,GAAG,IAAI,CAACjG,WAAW,CAACkG,cAAc,CAAC,CAAC;IACrD,OAAOD,WAAW,CAACxC,MAAM,KAAK,CAAC,IAAI,CAACwC,WAAW,CAAC,CAAC,CAAC,CAACG,OAAO;EAC5D;EAEOjE,WAAWA,CAAA,EAAY;IAC5B,OAAO,IAAI,CAACA,WAAW;EACzB;EAEOmE,cAAcA,CAAA,EAAW;IAC9B,IAAI,CAAC,IAAI,CAACnG,YAAY,EAAE,OAAO,CAAC;IAEhC,MAAMkF,SAAS,GAAG,IAAInB,UAAU,CAAC,IAAI,CAAC/D,YAAY,CAACmF,iBAAiB,CAAC;IACrE,IAAI,CAACnF,YAAY,CAACqF,oBAAoB,CAACH,SAAS,CAAC;IAEjD,IAAII,GAAG,GAAG,CAAC;IACX,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,SAAS,CAAC5B,MAAM,EAAEG,CAAC,EAAE,EAAE;MACzC6B,GAAG,IAAIJ,SAAS,CAACzB,CAAC,CAAC;IACrB;IAEA,OAAOL,IAAI,CAACqC,KAAK,CAAEH,GAAG,GAAGJ,SAAS,CAAC5B,MAAM,GAAG,GAAG,GAAI,GAAG,CAAC;EACzD;EAEO8C,OAAOA,CAAA,EAAS;IACrB,IAAI,CAACrB,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAE3B,IAAI,IAAI,CAAC/E,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACoG,UAAU,CAAC,CAAC;MACjC,IAAI,CAACpG,eAAe,GAAG,IAAI;IAC7B;IAEA,IAAI,IAAI,CAACD,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACqG,UAAU,CAAC,CAAC;MAC9B,IAAI,CAACrG,YAAY,GAAG,IAAI;IAC1B;IAEA,IAAI,IAAI,CAACD,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC4E,KAAK,KAAK,QAAQ,EAAE;MAC7D,IAAI,CAAC5E,YAAY,CAACuG,KAAK,CAAC,CAAC;MACzB,IAAI,CAACvG,YAAY,GAAG,IAAI;IAC1B;IAEA,IAAI,IAAI,CAACF,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC0G,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAC3D,IAAI,CAAC7G,WAAW,GAAG,IAAI;IACzB;IAEAsC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;;EAEA;EACA,MAAauE,cAAcA,CAAA,EAAqB;IAC9C,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMjG,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACzE8F,MAAM,CAACL,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACjD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,KAAK;IACd;EACF;EAEA,MAAawE,eAAeA,CAAA,EAA+B;IACzD,IAAI;MACF,MAAMC,OAAO,GAAG,MAAMnG,SAAS,CAACC,YAAY,CAACmG,gBAAgB,CAAC,CAAC;MAC/D,OAAOD,OAAO,CAACE,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC;IAC/D,CAAC,CAAC,OAAO7E,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX;EACF;AACF;AAEA,OAAO,MAAM8E,YAAY,GAAG,IAAIxH,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}