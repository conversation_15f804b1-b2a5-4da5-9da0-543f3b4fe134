{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"required\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport capitalize from '../utils/capitalize';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport formLabelClasses, { getFormLabelUtilityClasses } from './formLabelClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    focused,\n    disabled,\n    error,\n    filled,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, disabled && 'disabled', error && 'error', filled && 'filled', focused && 'focused', required && 'required'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormLabelUtilityClasses, classes);\n};\nexport const FormLabelRoot = styled('label', {\n  name: 'MuiFormLabel',\n  slot: 'Root',\n  overridesResolver: ({\n    ownerState\n  }, styles) => {\n    return _extends({}, styles.root, ownerState.color === 'secondary' && styles.colorSecondary, ownerState.filled && styles.filled);\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  color: (theme.vars || theme).palette.text.secondary\n}, theme.typography.body1, {\n  lineHeight: '1.4375em',\n  padding: 0,\n  position: 'relative',\n  [`&.${formLabelClasses.focused}`]: {\n    color: (theme.vars || theme).palette[ownerState.color].main\n  },\n  [`&.${formLabelClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled\n  },\n  [`&.${formLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})(({\n  theme\n}) => ({\n  [`&.${formLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\nconst FormLabel = /*#__PURE__*/React.forwardRef(function FormLabel(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormLabel'\n  });\n  const {\n      children,\n      className,\n      component = 'label'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['color', 'required', 'focused', 'disabled', 'error', 'filled']\n  });\n  const ownerState = _extends({}, props, {\n    color: fcs.color || 'primary',\n    component,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(FormLabelRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [children, fcs.required && /*#__PURE__*/_jsxs(AsteriskComponent, {\n      ownerState: ownerState,\n      \"aria-hidden\": true,\n      className: classes.asterisk,\n      children: [\"\\u2009\", '*']\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the label should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the label should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the input of this label is focused (used by `FormGroup` components).\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormLabel;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "formControlState", "useFormControl", "capitalize", "useDefaultProps", "styled", "formLabelClasses", "getFormLabelUtilityClasses", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "color", "focused", "disabled", "error", "filled", "required", "slots", "root", "asterisk", "FormLabelRoot", "name", "slot", "overridesResolver", "styles", "colorSecondary", "theme", "vars", "palette", "text", "secondary", "typography", "body1", "lineHeight", "padding", "position", "main", "AsteriskComponent", "props", "FormLabel", "forwardRef", "inProps", "ref", "children", "className", "component", "other", "muiFormControl", "fcs", "states", "as", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "oneOfType", "oneOf", "elementType", "bool", "sx", "arrayOf", "func"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/FormLabel/FormLabel.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"required\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport capitalize from '../utils/capitalize';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport formLabelClasses, { getFormLabelUtilityClasses } from './formLabelClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    focused,\n    disabled,\n    error,\n    filled,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, disabled && 'disabled', error && 'error', filled && 'filled', focused && 'focused', required && 'required'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormLabelUtilityClasses, classes);\n};\nexport const FormLabelRoot = styled('label', {\n  name: 'MuiFormLabel',\n  slot: 'Root',\n  overridesResolver: ({\n    ownerState\n  }, styles) => {\n    return _extends({}, styles.root, ownerState.color === 'secondary' && styles.colorSecondary, ownerState.filled && styles.filled);\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  color: (theme.vars || theme).palette.text.secondary\n}, theme.typography.body1, {\n  lineHeight: '1.4375em',\n  padding: 0,\n  position: 'relative',\n  [`&.${formLabelClasses.focused}`]: {\n    color: (theme.vars || theme).palette[ownerState.color].main\n  },\n  [`&.${formLabelClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled\n  },\n  [`&.${formLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})(({\n  theme\n}) => ({\n  [`&.${formLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\nconst FormLabel = /*#__PURE__*/React.forwardRef(function FormLabel(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormLabel'\n  });\n  const {\n      children,\n      className,\n      component = 'label'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['color', 'required', 'focused', 'disabled', 'error', 'filled']\n  });\n  const ownerState = _extends({}, props, {\n    color: fcs.color || 'primary',\n    component,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(FormLabelRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [children, fcs.required && /*#__PURE__*/_jsxs(AsteriskComponent, {\n      ownerState: ownerState,\n      \"aria-hidden\": true,\n      className: classes.asterisk,\n      children: [\"\\u2009\", '*']\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the label should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the label should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the input of this label is focused (used by `FormGroup` components).\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;AACvH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,gBAAgB,IAAIC,0BAA0B,QAAQ,oBAAoB;AACjF,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGP,UAAU;EACd,MAAMQ,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQjB,UAAU,CAACU,KAAK,CAAC,EAAE,EAAEE,QAAQ,IAAI,UAAU,EAAEC,KAAK,IAAI,OAAO,EAAEC,MAAM,IAAI,QAAQ,EAAEH,OAAO,IAAI,SAAS,EAAEI,QAAQ,IAAI,UAAU,CAAC;IACvJG,QAAQ,EAAE,CAAC,UAAU,EAAEL,KAAK,IAAI,OAAO;EACzC,CAAC;EACD,OAAOhB,cAAc,CAACmB,KAAK,EAAEZ,0BAA0B,EAAEK,OAAO,CAAC;AACnE,CAAC;AACD,OAAO,MAAMU,aAAa,GAAGjB,MAAM,CAAC,OAAO,EAAE;EAC3CkB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAAC;IAClBd;EACF,CAAC,EAAEe,MAAM,KAAK;IACZ,OAAO/B,QAAQ,CAAC,CAAC,CAAC,EAAE+B,MAAM,CAACN,IAAI,EAAET,UAAU,CAACE,KAAK,KAAK,WAAW,IAAIa,MAAM,CAACC,cAAc,EAAEhB,UAAU,CAACM,MAAM,IAAIS,MAAM,CAACT,MAAM,CAAC;EACjI;AACF,CAAC,CAAC,CAAC,CAAC;EACFW,KAAK;EACLjB;AACF,CAAC,KAAKhB,QAAQ,CAAC;EACbkB,KAAK,EAAE,CAACe,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACC,IAAI,CAACC;AAC5C,CAAC,EAAEJ,KAAK,CAACK,UAAU,CAACC,KAAK,EAAE;EACzBC,UAAU,EAAE,UAAU;EACtBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,UAAU;EACpB,CAAC,KAAK/B,gBAAgB,CAACQ,OAAO,EAAE,GAAG;IACjCD,KAAK,EAAE,CAACe,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACnB,UAAU,CAACE,KAAK,CAAC,CAACyB;EACzD,CAAC;EACD,CAAC,KAAKhC,gBAAgB,CAACS,QAAQ,EAAE,GAAG;IAClCF,KAAK,EAAE,CAACe,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACC,IAAI,CAAChB;EAC5C,CAAC;EACD,CAAC,KAAKT,gBAAgB,CAACU,KAAK,EAAE,GAAG;IAC/BH,KAAK,EAAE,CAACe,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACd,KAAK,CAACsB;EAC7C;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,iBAAiB,GAAGlC,MAAM,CAAC,MAAM,EAAE;EACvCkB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACe,KAAK,EAAEd,MAAM,KAAKA,MAAM,CAACL;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFO;AACF,CAAC,MAAM;EACL,CAAC,KAAKtB,gBAAgB,CAACU,KAAK,EAAE,GAAG;IAC/BH,KAAK,EAAE,CAACe,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACd,KAAK,CAACsB;EAC7C;AACF,CAAC,CAAC,CAAC;AACH,MAAMG,SAAS,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAMJ,KAAK,GAAGpC,eAAe,CAAC;IAC5BoC,KAAK,EAAEG,OAAO;IACdpB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFsB,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG;IACd,CAAC,GAAGP,KAAK;IACTQ,KAAK,GAAGtD,6BAA6B,CAAC8C,KAAK,EAAE5C,SAAS,CAAC;EACzD,MAAMqD,cAAc,GAAG/C,cAAc,CAAC,CAAC;EACvC,MAAMgD,GAAG,GAAGjD,gBAAgB,CAAC;IAC3BuC,KAAK;IACLS,cAAc;IACdE,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ;EACxE,CAAC,CAAC;EACF,MAAMxC,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAE6C,KAAK,EAAE;IACrC3B,KAAK,EAAEqC,GAAG,CAACrC,KAAK,IAAI,SAAS;IAC7BkC,SAAS;IACThC,QAAQ,EAAEmC,GAAG,CAACnC,QAAQ;IACtBC,KAAK,EAAEkC,GAAG,CAAClC,KAAK;IAChBC,MAAM,EAAEiC,GAAG,CAACjC,MAAM;IAClBH,OAAO,EAAEoC,GAAG,CAACpC,OAAO;IACpBI,QAAQ,EAAEgC,GAAG,CAAChC;EAChB,CAAC,CAAC;EACF,MAAMN,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,KAAK,CAACa,aAAa,EAAE3B,QAAQ,CAAC;IAChDyD,EAAE,EAAEL,SAAS;IACbpC,UAAU,EAAEA,UAAU;IACtBmC,SAAS,EAAE/C,IAAI,CAACa,OAAO,CAACQ,IAAI,EAAE0B,SAAS,CAAC;IACxCF,GAAG,EAAEA;EACP,CAAC,EAAEI,KAAK,EAAE;IACRH,QAAQ,EAAE,CAACA,QAAQ,EAAEK,GAAG,CAAChC,QAAQ,IAAI,aAAaT,KAAK,CAAC8B,iBAAiB,EAAE;MACzE5B,UAAU,EAAEA,UAAU;MACtB,aAAa,EAAE,IAAI;MACnBmC,SAAS,EAAElC,OAAO,CAACS,QAAQ;MAC3BwB,QAAQ,EAAE,CAAC,QAAQ,EAAE,GAAG;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGd,SAAS,CAACe,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEX,QAAQ,EAAE/C,SAAS,CAAC2D,IAAI;EACxB;AACF;AACA;EACE7C,OAAO,EAAEd,SAAS,CAAC4D,MAAM;EACzB;AACF;AACA;EACEZ,SAAS,EAAEhD,SAAS,CAAC6D,MAAM;EAC3B;AACF;AACA;AACA;AACA;EACE9C,KAAK,EAAEf,SAAS,CAAC,sCAAsC8D,SAAS,CAAC,CAAC9D,SAAS,CAAC+D,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE/D,SAAS,CAAC6D,MAAM,CAAC,CAAC;EACtK;AACF;AACA;AACA;EACEZ,SAAS,EAAEjD,SAAS,CAACgE,WAAW;EAChC;AACF;AACA;EACE/C,QAAQ,EAAEjB,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACE/C,KAAK,EAAElB,SAAS,CAACiE,IAAI;EACrB;AACF;AACA;EACE9C,MAAM,EAAEnB,SAAS,CAACiE,IAAI;EACtB;AACF;AACA;EACEjD,OAAO,EAAEhB,SAAS,CAACiE,IAAI;EACvB;AACF;AACA;EACE7C,QAAQ,EAAEpB,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACEC,EAAE,EAAElE,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACmE,OAAO,CAACnE,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC4D,MAAM,EAAE5D,SAAS,CAACiE,IAAI,CAAC,CAAC,CAAC,EAAEjE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC4D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}