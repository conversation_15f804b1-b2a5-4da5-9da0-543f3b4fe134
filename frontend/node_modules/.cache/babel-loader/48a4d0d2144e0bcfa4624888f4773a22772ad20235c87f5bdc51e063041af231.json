{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormControlUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormControl', slot);\n}\nconst formControlClasses = generateUtilityClasses('MuiFormControl', ['root', 'marginNone', 'marginNormal', 'marginDense', 'fullWidth', 'disabled']);\nexport default formControlClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getFormControlUtilityClasses", "slot", "formControlClasses"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/@mui/material/FormControl/formControlClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormControlUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormControl', slot);\n}\nconst formControlClasses = generateUtilityClasses('MuiFormControl', ['root', 'marginNone', 'marginNormal', 'marginDense', 'fullWidth', 'disabled']);\nexport default formControlClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,gBAAgB,EAAEE,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AACnJ,eAAeI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}