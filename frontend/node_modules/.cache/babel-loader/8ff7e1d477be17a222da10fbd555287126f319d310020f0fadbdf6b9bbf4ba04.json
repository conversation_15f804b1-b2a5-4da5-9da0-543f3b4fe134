{"ast": null, "code": "'use strict';\n\nvar isCallable = require('../internals/is-callable');\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};", "map": {"version": 3, "names": ["isCallable", "require", "module", "exports", "it"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/core-js-pure/internals/is-object.js"], "sourcesContent": ["'use strict';\nvar isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AAEpDC,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,OAAO,OAAOA,EAAE,IAAI,QAAQ,GAAGA,EAAE,KAAK,IAAI,GAAGJ,UAAU,CAACI,EAAE,CAAC;AAC7D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}